// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Click Through with OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="ClickThroughOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Click Through with PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="ClickThroughPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Click Through) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="ClickThrough"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath with OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="CloudpathOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath with PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="CloudpathPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath with proxy & OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="CloudpathProxyOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath with proxy & PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="CloudpathProxyPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath with proxy) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="CloudpathProxy"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Cloudpath) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="Cloudpath"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Guest Pass with OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="GuestPassOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Guest Pass with PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="GuestPassPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Guest Pass) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="GuestPass"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Host Approval with OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="HostApprovalOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Host Approval with PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="HostApprovalPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Host Approval) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="HostApproval"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr With OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WisprOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr With PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WisprPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr With alwaysAccept & OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WisprAlwaysAcceptOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr With alwaysAccept & PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WisprAlwaysAcceptPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr with alwaysAccept) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WisprAlwaysAccept"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (WISPr) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="Wispr"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctoffOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctoffPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting proxy and OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctproxyOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting proxy and PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctproxyPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting proxy) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctproxyNone"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting service and OWE) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctonOwe"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting service and PSK) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctonPsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow with accounting service) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctonNone"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal (Workflow) diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="WorkflowAcctoffNone"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Captive portal should render Captive portal default diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Captive Portal"
          src="ClickThrough"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - DPSK should render DPSK diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Dynamic Pre-Shared Key (DPSK)"
          src="Dpsk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - Hotspot20 should render Hotspot20 diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          src="Hotspot20"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - PSK should render AAA diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Enterprise AAA (802.1X)"
          src="Aaa"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram NetworkDiagram - PSK should render PSK diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          alt="Passphrase (PSK/SAE)"
          src="Psk"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`NetworkDiagram should render default diagram successfully 1`] = `
<DocumentFragment>
  .c0 {
  width: 358px;
  margin-top: 40px;
}

<div
    class="ant-row ant-row-center"
  >
    <div
      class="ant-col"
    >
      <div
        class="c0"
      >
        <img
          src="None"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;
