import { defineMessage, MessageDescriptor } from 'react-intl'

/* eslint-disable max-len */
export enum Features {
  ABAC_POLICIES_TOGGLE = 'abac-policies-toggle',
  ALARM_WITH_PRODUCT_FILTER_TOGGLE = 'alarm-with-product-filter-toggle',
  ALARM_CLEAR_ALARM_TOGGLE = 'alarm-clear-alarm-toggle',
  ALARM_CLEAR_ALL_ALARMS_TOGGLE = 'alarm-clear-all-alarms-toggle',
  AP_FIRMWARE_UPGRADE_BY_MODEL_TOGGLE = 'ap-fw-mgmt-upgrade-by-model',
  AP_MANAGEMENT_VLAN_AP_LEVEL_TOGGLE = 'removable-ff',
  AP_MESH_TOPOLOGY = 'acx-ui-ap-mesh-topology',
  APP_INSIGHTS = 'acx-ui-app-insights-toggle',
  ASSIGN_MULTI_EC_TO_MSP_ADMINS = 'mspservice-assign-multi-ec-to-multi-msp-admin',
  AP_TX_POWER_TOGGLE = 'ap-tx-power-toggle',
  BETA_BUTTON = 'acx-ui-beta-button-toggle',
  BULK_VLAN_PROVISIONING = 'acx-ui-bulk-vlan-provisioning-toggle',
  CANVAS = 'ruckus-gen-ai-customized-canvas',
  CLOUDPATH_BETA = 'BETA-CP',
  IDENTITY_UI_REFACTOR = 'identity-client-enabled',
  IDENTITY_GROUP_CONFIG_TEMPLATE = 'identity-group-config-template',
  CONFIG_CHANGE = 'removable-ff',
  CONFIG_TEMPLATE = 'acx-ui-config-template',
  CONFIG_TEMPLATE_DRIFTS = 'acx-ui-config-template-drifts',
  CONFIG_TEMPLATE_CLONE = 'acx-ui-config-template-clone',
  CONFIG_TEMPLATE_CLONE_P1 = 'acx-ui-config-template-clone-phase1',
  CONFIG_TEMPLATE_CLONE_VENUE = 'acx-ui-config-template-clone-venue',
  CONFIG_TEMPLATE_ENFORCED = 'acx-ui-config-template-enforcement',
  CONFIG_TEMPLATE_ENFORCED_P1 = 'acx-ui-config-template-enforcement-phase1',
  CONFIG_TEMPLATE_ENFORCED_VENUE = 'acx-ui-config-template-enforcement-venue',
  CONFIG_TEMPLATE_EXTRA = 'acx-ui-config-template-extra',
  CONFIG_TEMPLATE_NAME_DRAWER = 'acx-ui-config-template-name-drawer',
  CONFIG_TEMPLATE_DISPLAYABLE_ACTIVATION = 'acx-ui-config-template-displayable-activation',
  CERTIFICATE_TEMPLATE = 'certificate-template-service-enabled',
  MULTIPLE_CERTIFICATE_TEMPLATE = 'multiple-certificate-template',
  CONNECTION_METERING = 'connection-metering-enabled',
  DATA_PLANE = 'dataPlane',
  DEVICE_AGNOSTIC = 'entitlement-device-agnostic-sku-toggle',
  DPSK_PER_BOUND_PASSPHRASE_ALLOWED_DEVICE_INCREASED_LIMIT = 'dpsk-per-bound-passphrase-allowed-device-increased-limit',
  EARLY_ACCESS_FEATURE_LIST_TOGGLE = 'acx-ui-selective-early-access-toggle',
  EDGE_LAG = 'acx-ui-edges-lag-toggle',
  EDGES_DHCP_CSV_TOGGLE = 'acx-ui-edges-dhcp-csv-toggle',
  EDGES_PING_TRACEROUTE_TOGGLE = 'acx-ui-edges-ping-traceroute-toggle',
  EDGES_SCHEDULE_UPGRADE_TOGGLE = 'acx-ui-edges-schedule-upgrade-toggle',
  EDGES_SD_LAN_PHASE2_TOGGLE = 'acx-ui-edges-sdlan-phase2-toggle',
  EDGES_SD_LAN_TOGGLE = 'acx-ui-edges-centralized-forwarding-toggle',
  EDGES_SD_LAN_HA_TOGGLE = 'edge-sdlan-ha-toggle',
  EDGES_SUB_INTERFACE_CSV_TOGGLE = 'acx-ui-edges-sub-interface-csv-toggle',
  EDGES_TOGGLE = 'acx-ui-edges-toggle',
  EDGE_DHCP_HA_TOGGLE = 'edge-dhcp-ha-toggle',
  EDGE_FIREWALL_HA_TOGGLE = 'edge-firewall-ha-toggle',
  EDGE_PIN_HA_TOGGLE = 'edge-pin-ha-toggle',
  EDGE_PIN_ENHANCE_TOGGLE = 'edge-pin-enhance-toggle',
  EDGE_GRACEFUL_SHUTDOWN_TOGGLE = 'edge-graceful-shutdown-toggle',
  EDGE_VXLAN_TUNNEL_KA_TOGGLE = 'edge-vxlan-tunnel-ka-toggle',
  EDGE_SD_LAN_MV_TOGGLE = 'edge-sdlan-mv-toggle',
  EDGE_QOS_TOGGLE = 'edge-qos-toggle',
  EDGE_COMPATIBILITY_CHECK_TOGGLE = 'edge-compatibility-check-toggle',
  EDGE_HA_AA_FALLBACK_TOGGLE = 'edge-ha-aa-fallback-toggle',
  EDGE_HA_AA_DMZ_TOGGLE = 'edge-ha-aa-dmz-toggle',
  EDGE_HA_SUB_INTERFACE_TOGGLE = 'edge-ha-sub-interface-toggle',
  EDGE_FIRMWARE_NOTIFICATION_BATCH_OPERATION_TOGGLE= 'edge-firmware-notification-batch-operation-toggle',
  EDGE_MDNS_PROXY_TOGGLE = 'edge-mdns-proxy-toggle',
  EDGE_THIRDPARTY_MGMT_TOGGLE = 'edge-poc-thirdparty-mgmt-toggle',
  EDGE_NOKIA_OLT_MGMT_TOGGLE = 'edge-poc-nokia-olt-mgmt-toggle',
  EDGE_ARPT_TOGGLE = 'edge-arpt-toggle',
  EDGE_AV_REPORT_TOGGLE= 'edge-av-report-toggle',
  EDGE_NAT_TRAVERSAL_PHASE1_TOGGLE = 'edge-nat-traversal-phase1-toggle',
  EDGE_CORE_ACCESS_SEPARATION_TOGGLE = 'edge-core-access-separation-toggle',
  EDGE_ENG_COMPATIBILITY_CHECK_ENHANCEMENT_TOGGLE = 'edge-eng-compatibility-check-enhancement-toggle',
  EDGE_DUAL_WAN_TOGGLE = 'edge-dual-wan-toggle',
  EDGE_L2OGRE_TOGGLE = 'edge-l2ogre-toggle',
  EDGE_NETWORK_FILTER_TOGGLE = 'acx-ui-network-filter-edges-toggle',
  EDGE_MULTI_NAT_IP_TOGGLE = 'edge-multi-nat-ip-toggle',
  EDGE_DELEGATION_POC_TOGGLE = 'edge-delegation-poc-toggle',
  ENTITLEMENT_EXTENDED_TRIAL_TOGGLE = 'entitlement-acx-extended-trial-toggle',
  ENTITLEMENT_PENDING_ACTIVATION_TOGGLE = 'entitlement-pending-activation-toggle',
  ENTITLEMENT_ACTIVATE_PENDING_ACTIVATION_TOGGLE = 'entitlement-pending-activation-activate-toggle',
  ENTITLEMENT_ATTENTION_NOTES_TOGGLE = 'entitlement-attention-notes-toggle',
  ENTITLEMENT_COMPLIANCE_NOTES_TOGGLE = 'entitlement-no-courtesy-licenses-toggle',
  ENTITLEMENT_LICENSE_COMPLIANCE_TOGGLE= 'entitlement-license-compliance-toggle',
  ENTITLEMENT_RBAC_API = 'abac-policies-toggle',
  ENTITLEMENT_SEPARATE_SERVICEDATE_TOGGLE = 'entitlement-separate-trial-date-toggle',
  ENTITLEMENT_VIRTUAL_SMART_EDGE_TOGGLE = 'entitlement-virtual-smart-edge-toggle',
  EVENT_ALARM_META_TIME_RANGE_TOGGLE = 'event-alarm-meta-time-range-toggle',
  EOL_AP_2022_12_PHASE_2_TOGGLE = 'eol-ap-2022-12-phase-2-toggle',
  ETHERNET_PORT_PROFILE_TOGGLE = 'acx-ui-ethernet-toggle',
  ETHERNET_PORT_PROFILE_DVLAN_TOGGLE = 'acx-ui-ethernet-dvlan-toggle',
  ETHERNET_PORT_SUPPORT_PROXY_RADIUS_TOGGLE='acx-ethernet-port-support-proxy-radius-toggle',
  EXPORT_EVENTS_TOGGLE = 'acx-ui-scheduled-events-export-toggle',
  EXTEND_SSID_DESPRIPTION_TOGGLE = 'acx-ui-extend-ssid-description-toggle',
  G_MAP = 'acx-ui-maps-api-toggle',
  GOOGLE_WORKSPACE_SSO_TOGGLE = 'google-workspace-sso-toggle',
  GROUP_BASED_LOGIN_TOGGLE = 'group-based-login-toggle',
  HELP_SUPPORT = 'acx-ui-help-support',
  HOST_APPROVAL_EMAIL_LIST_TOGGLE = 'guest-host-approval-email-list-toggle',
  I18N_DATA_STUDIO_TOGGLE = 'acx-ui-i18n-data-studio-toggle',
  IDM_APPLICATION_KEY_TOGGLE = 'ptenant-application-key-toggle',
  IDM_DECOUPLING = 'ptenant-admin-authentication-enabled',
  INCIDENTS_AIRTIME_TOGGLE = 'removable-ff',
  INCIDENTS_SWITCH_DDOS_TOGGLE = 'acx-ui-tcp-syn-ddos-toggle',
  INCIDENTS_SWITCH_LOOP_DETECTION_TOGGLE = 'acx-ui-loop-detection-toggle',
  INCIDENTS_SWITCH_LLDP_STATUS_TOGGLE = 'acx-ui-lldp-status-toggle',
  INCIDENTS_SWITCH_PORT_FLAP_TOGGLE = 'acx-ui-port-flap-toggle',
  INCIDENTS_SWITCH_PORT_CONGESTION_TOGGLE = 'acx-ui-port-congestion-toggle',
  INCIDENTS_SWITCH_UPLINK_PORT_CONGESTION_TOGGLE = 'acx-ui-uplink-port-congestion-toggle',
  LINKEDIN_OIDC_TOGGLE = 'guest-linkedin-openid-connect-toggle',
  LOGIN_SSO_SIGNATURE_TOGGLE = 'login-sso-saml-signature',
  MFA_ALTERNATE_QR_CODE_TOGGLE = 'acx-ui-mfa-qr-code-display-toggle',
  MFA_NEW_API_TOGGLE = 'mfa-new-api-toggle',
  MSP_AGGREGATE_NOTIFICATION_TOGGLE = 'aggregate-notification-toggle',
  MSP_APP_MONITORING = 'mspservice-app-monitoring',
  MSP_BRAND_360 = 'acx-ui-msp-brand360-toggle',
  MSP_DATA_STUDIO = 'acx-ui-msp-data-studio-toggle',
  MSP_EC_CREATE_WITH_TIER = 'mspservice-mspec-create-pass-tier-info',
  MSP_HSP_SUPPORT = 'mspservice-hsp-01',
  MSP_HSP_360_PLM_FF = 'HSP-360',
  MSP_HSP_PLM_FF = 'HSP-LOC',
  MSP_MULTI_PROPERTY_CREATION_TOGGLE = 'msp-multi-property-creation-toggle',
  MSP_PATCH_TIER = 'mspservice-patch-tier',
  MSP_RBAC_API = 'abac-policies-toggle',
  MSP_SELF_ASSIGNMENT = 'entitlement-msp-own-license-assignment-toggle',
  MSP_SORT_ON_TP_COUNT_TOGGLE = 'mspservice-sort-on-tp-count-toggle',
  MSP_UPGRADE_MULTI_EC_FIRMWARE = 'mspservice-multi-ec-to-upgrade-ap-firmware',
  MSPEC_ALARM_COUNT_SUPPORT_TOGGLE = 'acx-ui-mspec-alarm-count-toggle',
  MSPEC_ALLOW_DELETE_ADMIN = 'mspservice-allow-last-ec-admin-deletion',
  MSPEC_OPTIONAL_ADMIN = 'mspservice-mspec-adminoptional',
  MONITORING_PAGE_LOAD_TIMES = 'acx-ui-monitoring-page-load-times-toggle',
  MULTIPLE_VAR_INVITATION_TOGGLE = 'ptenant-multiple-var-support-toggle',
  NETWORK_SEGMENTATION = 'acx-ui-network-segmentation-toggle',
  NETWORK_SEGMENTATION_SWITCH = 'switch-consumer-nsg-toggle',
  PORTAL_PROFILE_CONSOLIDATION_TOGGLE = 'acx-ui-consolidated-portal-profile-toggle',
  NEW_OS_VENDOR_IN_DEVICE_POLICY = 'acx-ui-new-os-vendor-in-device-policy-toggle',
  NOTIFICATION_ADMIN_CONTEXTUAL_TOGGLE = 'ptenant-admin-contextual-notifications',
  NOTIFICATION_CHANNEL_API_CHANGES_TOGGLE = 'nuvo-notification-api-changes-toggle',
  NOTIFICATION_CHANNEL_SELECTION_TOGGLE = 'nuvo-notification-channel-selection',
  NUKETENANT_SOFT_TENANT_DELETE_TOGGLE = 'nuketenant-soft-tenant-delete-toggle',
  NUVO_SMS_PROVIDER_TOGGLE = 'nuvo-customer-owned-sms-provider',
  NUVO_SMS_GRACE_PERIOD_TOGGLE = 'nuvo-sms-grace-period',
  NUVO_SMS_MESSAGING_SERVICE_TOGGLE = 'nuvo-messaging-service-toggle',
  PLM_FF = 'ACX-PLM-FF',
  PTENANT_RBAC_DPSK_ROLE_INTRODUCTION = 'ptenant-rbac-dpsk-role-introduction',
  PTENANT_RBAC_API = 'abac-policies-toggle',
  RADIUS_CLIENT_CONFIG = 'radius-client-config-api-enabled',
  RBAC_PHASE2_SSO_TOGGLE = 'acx-ui-rbac-phase2-toggle',
  RBAC_PHASE2_TOGGLE = 'acx-ui-rbac-phase21-toggle',
  RBAC_OPERATIONS_API_TOGGLE = 'acx-ui-rbac-allow-operations-api-toggle',
  RBAC_PHASE3_TOGGLE = 'acx-ui-rbac-phase3-toggle',
  ROGUE_EVENTS_FILTER = 'rogue-events-filter-enabled',
  RUCKUS_AI_INCIDENTS_AIRTIME_TOGGLE = 'removable-ff',
  RUCKUS_AI_SWITCH_HEALTH_TOGGLE = 'ruckus-ai-switch-health-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_DDOS_TOGGLE = 'ruckus-ai-tcp-syn-ddos-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_LOOP_DETECTION_TOGGLE = 'ruckus-ai-loop-detection-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_LLDP_STATUS_TOGGLE = 'ruckus-ai-lldp-status-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_PORT_FLAP_TOGGLE = 'ruckus-ai-port-flap-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_PORT_CONGESTION_TOGGLE = 'ruckus-ai-port-congestion-toggle',
  RUCKUS_AI_INCIDENTS_SWITCH_UPLINK_PORT_CONGESTION_TOGGLE = 'ruckus-ai-uplink-port-congestion-toggle',
  HEALTH_WIRED_TOPN_WITH_OTHERS = 'acx-ui-health-wired-topn-with-others-toggle',
  RUCKUS_AI_SWITCH_HEALTH_10010E_TOGGLE = 'ruckus-ai-switch-health-10010e-toggle',
  RUCKUS_WAN_GATEWAY_UI_SHOW = 'ruckus-wan-gateway-ui-show',
  RUCKUS_ONBOARDING_ASSISTANT_TOGGLE = 'ruckus-gen-ai-onboarding-assistant',
  SUBSCRIPTIONS_PAGESIZE_TOGGLE = 'acx-ui-subscriptions-pagesize-toggle',
  SSO = 'ADMN-SSO',
  SSO_SAML_ENCRYPTION = 'acx-ui-sso-saml-encryption',
  SUPPORT_DELEGATE_MSP_DASHBOARD_TOGGLE = 'acx-ui-support-to-msp-dashboard-toggle',
  SWITCH_FIRMWARE_RELATED_TSB_BLOCKING_TOGGLE = 'removable-ff',
  SWITCH_HEALTH_TOGGLE = 'acx-ui-switch-health-toggle',
  SWITCH_HEALTH_10010E_TOGGLE = 'acx-ui-switch-health-10010e-toggle',
  SWUTCH_MENBERS_QUERY_OPTIMIZATION = 'acx-ui-switch-members-query-optimization-toggle',
  SWITCH_PORT_HYPERLINK = 'removable-ff',
  SWITCH_RBAC_API = 'acx-ui-rbac-api-switch-toggle',
  SWITCH_STACK_NAME_DISPLAY_TOGGLE = 'switch-stack-name-display-toggle',
  SWITCH_STACK_UNIT_LIMITATION = 'removable-ff',
  SWITCH_SUPPORT_ICX8100= 'acx-ui-switch-support-icx8100-toggle',
  SWITCH_LEVEL_CLI_PROFILE = 'switch-consumer-switch-level-cli-profile-toggle',
  SWITCH_CABLE_TEST = 'switch-cable-testing-toggle',
  SWITCH_FLEXIBLE_AUTHENTICATION = 'switch-consumer-flexible-authentication-toggle',
  SWITCH_SUPPORT_ICX8200AV= 'removable-ff',
  SWITCH_UPDATE_RSTP_ABOVE_10020A = 'switch-update-rstp-above-10020a-toggle',
  TECH_PARTNER_ASSIGN_ECS = 'mspservice-techpartner-assign-ecs',
  TECH_PARTNER_GET_MSP_CUSTOMERS_TOGGLE = 'viewmodel-techpartner-getmspcustomers',
  VENUE_TAG_TOGGLE = 'acx-ui-venue-tag-toggle',
  VIEWMODEL_APIS_MIGRATE_MSP_TOGGLE = 'acx-ui-viewmodel-apis-migrate-msp',
  VIEWMODEL_TP_LOGIN_ADMIN_COUNT = 'viewmodel-query-tenantadmindelegation-tp-login',
  WEBHOOK_TOGGLE = 'acx-ui-webhook-toggle',
  VIEWMODEL_MSPEC_QUERY_TWO_FILTERS_TOGGLE = 'viewmodel-mspec-query-two-filters-toggle',
  WIFI_DOWNGRADE_VENUE_ABF_TOGGLE = 'removable-ff',
  WIFI_DISPLAY_MORE_AP_POE_PROPERTIES_TOGGLE = 'wifi-display-more-ap-poe-properties-toggle',
  WIFI_EDA_CLIENT_REVOKE_TOGGLE = 'wifi-eda-client-revoke-toggle',
  WIFI_EDA_QOS_MIRRORING_TOGGLE = 'wifi-eda-qos-mirroring-toggle',
  WIFI_EDA_WIFI6_AND_WIFI7_FLAG_TOGGLE = 'wifi-eda-wifi6-and-wifi7-flag-toggle',
  WIFI_EDA_WIFI7_320MHZ = 'wifi-eda-wifi7-320mhz-toggle',
  WIFI_EDA_WIFI7_MLO_TOGGLE = 'wifi-eda-wifi7-mlo-toggle',
  WIFI_EDA_WPA3_DSAE_TOGGLE = 'wifi-eda-wpa3-dsae-toggle',
  WIFI_EDA_TLS_KEY_ENHANCE_MODE_CONFIG_TOGGLE = 'removable-ff',
  WIFI_FR_HOTSPOT20_R1_TOGGLE = 'wifi-fr-hotspot20-r1-toggle',
  WIFI_SWITCHABLE_RF_TOGGLE = 'wifi-switchable-rf-toggle',
  WIFI_ANTENNA_TYPE_TOGGLE = 'wifi-antenna-type-selection-toggle',
  WIFI_RBAC_API = 'acx-ui-rbac-api-wifi-toggle',
  WIFI_POWER_SAVING_MODE_TOGGLE = 'wifi-power-saving-mode-indicator-toggle',
  WIFI_SOFTGRE_OVER_WIRELESS_TOGGLE = 'wifi-softgre-over-wireless-toggle',
  WIFI_IPSEC_PSK_OVER_NETWORK_TOGGLE = 'wifi-ipsec-psk-over-network',
  WIFI_RADSEC_TOGGLE = 'wifi-radsec-toggle',
  WIFI_OWE_TRANSITION_FOR_6G = 'wifi-owe-transition-for-6g',
  WIFI_DPSK3_NON_PROXY_MODE_TOGGLE='wifi-dpsk3-non-proxy-mode-toggle',
  WIFI_DIRECTORY_PROFILE_REUSE_COMPONENT_TOGGLE='wifi-directory-profile-reuse-component-toggle',
  ZERO_TOUCH_MESH = 'wifi-eda-zero-touch-mesh-toggle',
  EDGE_HA_TOGGLE = 'edge-ha-toggle',
  EDGE_HA_AA_TOGGLE = 'edge-ha-aa-toggle',
  WIFI_EDA_BRANCH_LEVEL_SUPPORTED_MODELS_TOGGLE = 'wifi-eda-branch-level-supported-models-toggle',
  SWITCH_NEXT_GENERATION_TOPOLOGY_TOGGLE = 'switch-next-generation-topology-toggle',
  RUCKUS_AI_USERS_TOGGLE = 'ruckus-ai-sso-toggle',
  WORKFLOW_ONBOARD = 'WORKFLOW-ONBOARD',
  WORKFLOW_TOGGLE = 'workflow-framework-enabled',
  WORKFLOW_CERTIFICATE_TEMPLATE_ACTION = 'workflow-cert-template-enabled',
  AP_FW_MGMT_UPGRADE_BY_MODEL = 'ap-fw-mgmt-upgrade-by-model',
  WIFI_8021X_MAC_AUTH_TOGGLE = 'removable-ff',
  RUCKUS_AI_NEW_ROLES_TOGGLE = 'ruckus-ai-users-new-roles-toggle',
  SESSION_DURATION_TOGGLE = 'wifi-guest-pass-portal-session-duration-toggle',
  WIFI_INCREASE_RADIUS_INSTANCE_1024 = 'removable-ff',
  RBAC_SERVICE_POLICY_TOGGLE = 'acx-ui-rbac-service-policy-toggle',
  GUEST_EMAIL_OTP_SELF_SIGN_TOGGLE = 'guest-email-otp-self-sign-toggle',
  WHATSAPP_SELF_SIGN_IN_TOGGLE = 'whatsapp-self-sign-in-toggle',
  WIFI_6G_INDOOR_OUTDOOR_SEPARATION = 'wifi-ap-6g-channel-separation-toggle',
  GUEST_MANUAL_PASSWORD_TOGGLE = 'guest-manual-password-toggle',
  RUCKUS_AI_JWT_TOGGLE = 'ruckus-ai-jwt-toggle',
  RBAC_CONFIG_TEMPLATE_TOGGLE = 'acx-ui-rbac-config-template-toggle',
  WIFI_EDA_WIFI7_MLO_3LINK_TOGGLE='wifi-eda-wifi7-mlo-3link-toggle',
  WIFI_MESH_CONFIGURATION_FOR_5G_6G_ONLY = 'wifi-mesh-configuration-for-5g-6g-only-toggle',
  SWITCH_AP_PORT_HYPERLINK ='switch-ap-port-hyperlink-toggle',
  WIFI_SNMP_V3_AGENT_PASSPHRASE_COMPLEXITY_TOGGLE = 'wifi-snmp-v3-agent-passphrase-complexity-toggle',
  WIFI_RESET_AP_LAN_PORT_TOGGLE = 'wifi-reset-ap-port-setting-toggle',
  IOT_MQTT_BROKER_TOGGLE = 'iot-mqtt-broker-toggle',
  IOT_PHASE_2_TOGGLE = 'iot-phase-2-toggle',
  WIFI_SOFTGRE_GATEWAY_FAILBACK_TOGGLE = 'wifi-softgre-gateway-failback-toggle',
  WIFI_AP_REBOOT_TIMEOUT_WLAN_TOGGLE = 'wifi-ap-reboot-timeout-wlan-toggle',
  WIFI_AP_DEFAULT_6G_ENABLEMENT_TOGGLE = 'wifi-ap-default-6g-enablement-toggle',
  WIFI_SMART_MONITOR_DISABLE_WLAN_TOGGLE = 'wifi-smart-monitor-disable-wlan-toggle',
  WIFI_AP_STICKY_CLIENT_STEERING_TOGGLE = 'wifi-ap-sticky-client-steering-toggle',
  WIFI_AP_POE_OPERATING_MODE_SETTING_TOGGLE = 'wifi-ap-poe-operating-mode-setting-toggle',
  WIFI_CAPTIVE_PORTAL_DIRECTORY_SERVER_TOGGLE = 'wifi-captive-portal-directory-server-toggle',
  WIFI_CAPTIVE_PORTAL_SSO_SAML_TOGGLE = 'wifi-captive-portal-sso-saml-toggle',
  WIFI_OVER_THE_DS_FT_SUPPORT_TOGGLE = 'wifi-over-the-ds-ft-support-toggle',
  WIFI_COMPATIBILITY_BY_MODEL = 'wifi-compatibility-check-by-model-toggle',
  WIFI_ETHERNET_SOFTGRE_TOGGLE = 'wifi-ethernet-softgre-toggle',
  WIFI_ETHERNET_DHCP_OPTION_82_TOGGLE = 'wifi-ethernet-dhcp-option-82-toggle',
  WIFI_ETHERNET_CLIENT_ISOLATION_TOGGLE = 'wifi-ethernet-client-isolation-toggle',
  ENTITLEMENT_LICENSE_COMPLIANCE_PHASE2_TOGGLE = 'entitlement-license-compliance-phase2-toggle',
  BRAND360_MDU_TOGGLE = 'acx-ui-brand360-mdu-toggle',
  SERVER_CERTIFICATE_MANAGEMENT_UI_TOGGLE = 'server-certificate-management-ui-toggle',
  WIFI_NETWORK_APPLICATION_CONTROL = 'wifi-network-application-control',
  WIFI_NETWORK_RADIUS_ACCOUNTING_TOGGLE='wifi-network-radius-accounting-toggle',
  WIFI_NETWORK_VENUE_QUERY = 'wifi-eda-network-venue-query-toggle',
  UX_OPTIMIZATION_FEATURE_TOGGLE = 'acx-ui-ux-optimization-feature-toggle',
  ROAMING_TYPE_EVENTS_TOGGLE = 'acx-ui-roaming-type-events-toggle',
  POLICY_IDENTITY_TOGGLE = 'policy-identity-toggle',
  SWITCH_CONSUMER_PORT_PROFILE_TOGGLE = 'switch-consumer-port-profile-toggle',
  ACX_UI_GATEWAYS_MENU_OPTION_TOGGLE = 'acx-ui-gateways-menu-option-toggle',
  BTM_EVENTS_TOGGLE = 'acx-ui-btm-events-toggle',
  RUCKUS_AI_BTM_EVENTS_TOGGLE = 'ruckus-ai-btm-events-toggle',
  MLISA_4_11_0_TOGGLE = 'acx-ui-mlisa-4-11-0-toggle',
  ANALYTIC_SNAPSHOT_TOGGLE = 'removable-ff',
  VIEWMODEL_UI_EC_INVENTORIES_QUERY_PERFORMANCE_CHANGES_TOGGLE = 'viewmodel-ui-ec-inventories-query-performance-changes-toggle',
  RUCKUS_AI_INTENT_AI_CONFIG_CHANGE_TOGGLE = 'ruckus-ai-intent-ai-config-change-toggle',
  INTENT_AI_CONFIG_CHANGE_TOGGLE = 'acx-ui-intent-ai-config-change-toggle',
  WIFI_R370_TOGGLE = 'wifi-r370-toggle',
  MAC_REGISTRATION_REQUIRE_IDENTITY_GROUP_TOGGLE = 'mac-registration-require-identity-group',
  DPSK_REQUIRE_IDENTITY_GROUP = 'dpsk-require-identity-group',
  AP_USB_PORT_SUPPORT_TOGGLE = 'wifi-usb-port-support-toggle',
  ACX_UI_NON_PROXY_ACCOUNTING_DPSK_TOGGLE = 'acx-ui-non-proxy-accounting-dpsk-toggle',
  AP_FW_MGMT_EARLY_ACCESS_TOGGLE = 'ap-fw-mgmt-early-access-toggle',
  ACX_UI_DATE_RANGE_LIMIT = 'acx-ui-date-range-selection-limit-30-days-toggle',
  ACX_UI_DATE_RANGE_RESET_MSG ='acx-ui-date-range-event-limit-90-days-toggle',
  ACX_UI_REPORTS_CORE_TIER_TOGGLE = 'acx-ui-reports-core-tier-toggle',
  ACX_UI_HISTORICAL_CLIENTS_DATE_RANGE_LIMIT ='acx-ui-historical-clients-date-range-limit-90-days-toggle',
  ACX_UI_MULTIPLE_AP_DHCP_MODE_WARNING ='acx-ui-multiple-ap-dhcp-mode-warning-toggle',
  VENUE_TABLE_ADD_STATUS_COLUMN = 'acx-ui-venue-table-add-status-column-toggle',
  NETWORK_PSK_MACAUTH_DYNAMIC_VLAN_TOGGLE = 'wifi-psk-dynamic-vlan-toggle',
  RUCKUS_AI_PREVENT_COLD_TIER_QUERY_TOGGLE = 'ruckus-ai-prevent-cold-tier-query-toggle',
  ACX_UI_PREVENT_COLD_TIER_QUERY_TOGGLE = 'acx-ui-prevent-cold-tier-query-toggle',
  WIFI_NAS_ID_HOTSPOT20_TOGGLE = 'wifi-nas-id-for-hotspot20-network',
  ACX_UI_DATA_SUBSCRIPTIONS_TOGGLE = 'acx-ui-data-subscriptions-toggle',
  RUCKUS_AI_DATA_SUBSCRIPTIONS_TOGGLE = 'ruckus-ai-data-subscriptions-toggle',
  ENTITLEMENT_SOLUTION_TOKEN_TOGGLE = 'entitlement-solution-token-toggle',
  ENTITLEMENT_ADAPTIVE_POLICY_TOGGLE='entitlement-adaptive-policy-toggle',
  ENTITLEMENT_PMS_INTEGRATION_TOGGLE='entitlement-pms-integration-toggle',
  ENTITLEMENT_SIS_INTEGRATION_TOGGLE='entitlement-sis-integration-toggle',
  ENTITLEMENT_HYBRID_CLOUD_SECURITY_TOGGLE='entitlement-hybrid-cloud-security-toggle',
  ACX_UI_SHOW_ADMIN_TYPE_COLUMN_TOGGLE='acx-ui-show-admin-type-column-toggle',
  ACX_UI_VENUE_CHANNEL_SELECTION_MANUAL = 'acx-ui-venue-channel-selection-manual-toggle',
  SWITCH_RSTP_PT_TO_PT_MAC_TOGGLE = 'switch-rstp-pt-to-pt-mac-toggle',
  LEGACY_ETHERNET_PORT_TOGGLE = 'wifi-ethernet-ports-template-legacy',
  ETHERNET_PORT_TEMPLATE_TOGGLE = 'wifi-ethernet-port-profile-template',
  MULTIPLE_IDENTITY_UNITS = 'multiple-identity-units',
  DPSK_PASSPHRASE_LENGTH_ENFORCEMENT = 'dpsk-passphrase-length-enforcement',
  WORKFLOW_TEMPLATE_TOGGLE = 'workflow-templating-enabled',
  PRECONFIGURED_HS20_IDP_TOGGLE = 'wifi-preconfigured-hotspot20-idps-toggle',
  SWITCH_ERROR_DISABLE_STATUS = 'switch-indicate-error-disable-status-toggle',
  SWITCH_ERROR_DISABLE_RECOVERY_TOGGLE = 'switch-error-disable-recovery-toggle',
  WIFI_CAPTIVE_PORTAL_OWE_TRANSITION = 'wifi-captive-portal-owe-transition',
  LOGIN_SSO_SAML_TECHPARTNER = 'login-sso-saml-techpartner',
  RA_PRIVACY_SETTINGS_APP_VISIBILITY_TOGGLE = 'ra-privacysettings-app-visibility-toggle',
  WIFI_IDENTITY_AND_IDENTITY_GROUP_MANAGEMENT_TOGGLE = 'wifi-identity-and-identity-group-management-toggle',
  SWITCH_SUPPORT_ICX8100X = 'switch-support-icx8100x-toggle',
  PTENANT_TO_COMMON_ACCOUNT_MANAGEMENT_TOGGLE = 'ptenant-to-common-account-management-toggle',
  ENTITLEMENT_PIN_FOR_IDENTITY_TOGGLE = 'entitlement-pin-for-identity-toggle',
  MSP_APP_VISIBILITY = 'mspservice-app-visibility',
  AI_DRIVEN_RRM_METRICS_TOGGLE = 'acx-ui-ai-driven-rrm-metrics-toggle',
  RUCKUS_AI_AI_DRIVEN_RRM_METRICS_TOGGLE = 'ruckus-ai-ai-driven-rrm-metrics-toggle',
  SWITCH_SUPPORT_ICX7550Zippy = 'switch-support-icx7550-zippy-toggle',
  LOGIN_SSO_SAML_MSPEC = 'login-sso-saml-mspec',
  DURGA_TENANT_CONVERSION_REC_TO_MSP_REC = 'durga-tenant-conversion-rec-to-msp-rec',
  SWITCH_SUPPORT_MAC_ACL_TOGGLE = 'switch-support-mac-acl-toggle',
  WIFI_WIRED_CLIENT_VISIBILITY_TOGGLE = 'wifi-wired-client-visibility-for-lanport-toggle',
  WIFI_AP_GROUP_MORE_PARAMETER_PHASE1_TOGGLE = 'wifi-ap-group-more-parameter-phase1-toggle',
  SUPPORT_STACK_NEIGHBOR_PORT_TOGGLE = 'acx-ui-support-stack-neighbor-port-toggle',
  WIFI_OPEN_NETWORK_INTEGRATE_IDENTITY_GROUP_TOGGLE = 'wifi-open-network-integrate-identity-group-toggle',
  IDENTITY_COMMON_ATTRIBUTES_TOGGLE = 'acx-ui-identity-common-attributes-toggle',
  WIFI_AP_EXTERNAL_ANTENNA_TOGGLE = 'wifi-ap-external-antenna-toggle',
  WORKFLOW_ENHANCED_VALIDATION_ENABLED = 'workflow-enhanced-validation-enabled',
  WIFI_EDA_IP_MODE_CONFIG_TOGGLE = 'wifi-eda-ip-mode-config-toggle',
  ENTITLEMENT_IOT_CTRL_TOGGLE = 'entitlement-iot-ctrl-toggle',
  ACX_UI_COUNTRYCODE_SEYCHELLES_TOGGLE = 'acx-ui-countrycode-seychelles-toggle',
  NEW_SERVICE_CATALOG = 'acx-ui-new-service-catalog',
  DEVICE_PROVISION_MANAGEMENT = 'device-provision-management',
  SSO_GROUP_LIMIT100 = 'sso-group-limit100',
  MDNS_PROXY_CONSOLIDATION = 'acx-ui-consolidated-mdns-proxy-toggle',
  RUCKUS_AI_ENERGY_SAVING_TOGGLE = 'ruckus-ai-energy-saving-toggle',
  ACX_UI_ENERGY_SAVING_TOGGLE = 'acx-ui-energy-saving-toggle',
  ACX_UI_USE_PAGIATED_PRIVILEGE_GROUP_API = 'acx-ui-use-paginated-privilege-group-api',
  ACX_UI_PRIVILEGE_GROUP_CUSTOMERS_LIST_ENHANCEMENT = 'acx-ui-privilege-group-customers-list-enhancement',
  MSP_HSP_DISPLAY_UID_TOGGLE = 'mspservice-display-hsp-uid-toggle',
  DHCP_CONSOLIDATION = 'acx-ui-consolidated-dhcp-toggle',
  MSPSERVICE_TIER_UPDATE_DEFAULTS_CONTROL = 'mspservice-tier-update-defaults-control',
  WORKFLOW_SAML_AUTH_ACTION = 'workflow-saml-authentication',
  SWITCH_SUPPORT_TIME_BASED_POE_TOGGLE = 'switch-support-time-based-poe-toggle',
  WIFI_WORKFLOW_CAPTIVE_PORTAL_NETWORK_TOGGLE = 'wifi-workflow-captive-portal-network-toggle',
  SWITCH_SUPPORT_LAG_FORCE_UP_TOGGLE = 'switch-support-lag-force-up-toggle'
}

export enum TierFeatures { // for Tier (ex: Beta) feature flag
  AP_70 = 'AP-70',
  BETA_DPSK3 = 'BETA-DPSK3',
  SMART_EDGES = 'PLCY-EDGE',
  CONFIG_TEMPLATE = 'CONFIG-TEMPLATE',
  WORKFLOW_ONBOARD = 'WORKFLOW-ONBOARD',
  LOCATION_BASED_SERVICES = 'LOCATION-BASED-SERVICES',
  PROXY_RADSEC = 'PROXY-RADSEC',
  RBAC_IMPLICIT_P1 = 'RBAC-IMPLICIT-P1',
  EDGE_ADV = 'EDGE-ADV',
  EDGE_AV_REPORT = 'EDGE-AV-REPORT',
  EDGE_NAT_T = 'EDGE-NAT-T',
  EDGE_ARPT = 'EDGE-ARPT',
  EDGE_MDNS_PROXY = 'EDGE-MDNS-PROXY',
  EDGE_HQOS = 'EDGE-HQOS',
  EDGE_L2OGRE = 'EDGE-L2OGRE',
  EDGE_NAT_IP_POOL = 'EDGE-NAT-IP-POOL',
  EDGE_DUAL_WAN = 'EDGE-DUAL-WAN',
  // for testing only
  TEST_SELECTIVE_BETA_01 = 'TEST-SELECTIVE-BETA-01',
  TEST_SELECTIVE_BETA_02 = 'TEST-SELECTIVE-BETA-02',
  TEST_SELECTIVE_BETA_03 = 'TEST-SELECTIVE-BETA-03',
  SERVICE_CATALOG_UPDATED = 'SERVICE-CATALOG-UPDATED'
}

interface BetaList {
  key: TierFeatures
  description: MessageDescriptor
  status: boolean
}

// This is Mandatory for Beta features list...
// When every we add a TierFeatures enum value above we need it's related
// description details and status value - true/false to show/hide
// from displaying in UI drawer component BetaFeaturesDrawer.
// If we don't have a description blurb and
// don't want it to be displayed then add status = false
export const BetaListDetails:BetaList[] = [
  { key: TierFeatures.WORKFLOW_ONBOARD, description: defineMessage({ defaultMessage: 'Workflows: ' }), status: false },
  { key: TierFeatures.BETA_DPSK3, description: defineMessage({ defaultMessage: 'DPSK3: Dynamic Preshared Keys working with WPA3-DSAE. Users connect their devices to a WPA2/WPA3 network with DPSK and are automatically moved to the WPA3 WLAN, allowing DPSK operation with WiFi 6e or WiFi7. DPSK3 allows the customer to take advantage of the flexibility of DPSK with the security of WPA3.' }), status: true },
  { key: TierFeatures.AP_70, description: defineMessage({ defaultMessage: 'AP-70: Wi-Fi 7 - Wi-Fi 7 UI configuration available for early adopters and customers provided with advance units of the R770. Contact your reseller for more information on availability of the new R770!' }), status: true },
  { key: TierFeatures.SMART_EDGES, description: defineMessage({ defaultMessage: 'RUCKUS Edge: RUCKUS Edge is a platfrom to run RUCKUS services on. Network administrators can utilize SD-LAN service or Personal Identity Networking service on a RUCKUS Edge. SD-LAN provides WLAN tunnelling using VXLAN. This will provide end users a seamless roaming experience across a network. The Personal Identity Networking service provides individual networks for users which is typically used in a multi-dwelling facility.' }), status: true },
  { key: TierFeatures.CONFIG_TEMPLATE, description: defineMessage({ defaultMessage: 'Config Template: It allows MSP users to create templates for networks, <venuePlural></venuePlural>, services, and policies. These templates can then be applied to multiple customers, providing a centralized and efficient solution for managing RUCKUS brand network equipment across properties.' }), status: true },
  { key: TierFeatures.LOCATION_BASED_SERVICES, description: defineMessage({ defaultMessage: 'LBS: RUCKUS One now lets you create a Location Based Service (LBS) Server linked to multiple <venuePlural></venuePlural> for efficient tracking and management of location-specific information. This feature gathers location data from connected access points (APs) and routes it to third-party application servers. You can add and manage LBS server profiles and enable Location Based Services for any <venueSingular></venueSingular> directly from the RUCKUS One web interface, enhancing your <venueSingular></venueSingular> management capabilities.' }), status: true },
  { key: TierFeatures.PROXY_RADSEC, description: defineMessage({ defaultMessage: 'R1 Proxy RadSec, CoA and DM: RadSec, securely transmits RADIUS messages over TLS to protect against interception and tampering across untrusted networks. CoA, dynamically updates user session attributes in real time without requiring re-authentication or disconnection. DM, instantly terminates a user session to enforce security or policy actions with minimal delay.' }), status: true },
  { key: TierFeatures.RBAC_IMPLICIT_P1, description: defineMessage({ defaultMessage: 'Role-based access control: Role-based access control (RBAC) and attributes-based access control (ABAC) are functionalities that provide a structured and efficient approach to managing administrator permissions and access rules in RUCKUS One.' }), status: true },
  { key: TierFeatures.EDGE_ADV, description: defineMessage({ defaultMessage: 'Personal Identity Networks: Personal Identity Networks (PIN) use VxLAN tunneling to extend Wi-Fi client access to the RUCKUS Edge, creating seamless connectivity across the network domain. It enables Wi-Fi clients to securely access their networks and connected devices while also establishing Personal Area Networks (PAN) for secure, individualized connectivity. PINs can be configured for any <venueSingular></venueSingular> that has Property Management enabled and has RUCKUS APs, ICX switches, and Edge devices deployed.' }), status: true },
  { key: TierFeatures.EDGE_AV_REPORT, description: defineMessage({ defaultMessage: 'Edge Application Report: This feature introduces the Deep Packet Inspection (DPI) module into RUCKUS Edge and an Application Visibility (AV) report on Ruckus One. It provides detailed application visibility into different types of applications running on the network, enabling administrators to gain insights into network traffic.' }), status: true },
  { key: TierFeatures.EDGE_NAT_T, description: defineMessage({ defaultMessage: 'Edge NAT-T Support: Enables NAT Traversal (NAT-T) for VxLAN-GPE tunnels between RUCKUS APs and RUCKUS Edge, allowing APs located behind a NAT router to establish tunnel connections.' }), status: true },
  { key: TierFeatures.EDGE_ARPT, description: defineMessage({ defaultMessage: 'Edge ARP Termination: The RUCKUS Edge Device intercepts ARP requests, responding on behalf of target IPs using IP/MAC mappings learned from ARP traffic. This enhances network efficiency by controlling and reducing ARP broadcast traffic, contributing to a more efficient wireless environment.' }), status: true },
  { key: TierFeatures.EDGE_MDNS_PROXY, description: defineMessage({ defaultMessage: 'mDNS Proxy for RUCKUS Edge: RUCKUS Edge mDNS gateway enables seamless service discovery across VLANs by overcoming Bonjour/mDNS’s Layer 2 limitations. It records services and processes client requests, allowing devices to discover and access services across network segments.' }), status: true },
  { key: TierFeatures.EDGE_HQOS, description: defineMessage({ defaultMessage: 'Edge HQoS Bandwidth: RUCKUS Edge HQoS-Driven Egress Scheduling to enforce strict SLAs, intelligent bandwidth management, LLQ prioritization, and DSCP-based classification for seamless, end-to-end QoS.' }), status: true },
  { key: TierFeatures.EDGE_L2OGRE, description: defineMessage({ defaultMessage: 'Edge Tunnel Profile support L2GRE tunnel type' }), status: false },
  { key: TierFeatures.SERVICE_CATALOG_UPDATED, description: defineMessage({ defaultMessage: 'Service Catalog (Updated): Introduces a new layout and filtering logic for network services and policies, enabling clearer visibility and easier access to unified services for RUCKUS network deployments.' }), status: true },
  { key: TierFeatures.EDGE_NAT_IP_POOL, description: defineMessage({ defaultMessage: 'Edge NAT IP Pool: Enables NAT IP pooling, allowing more client sessions to be supported by dynamically allocating public IP addresses from a shared pool.' }), status: false },
  { key: TierFeatures.EDGE_DUAL_WAN, description: defineMessage({ defaultMessage: 'Dual-WAN for RUCKUS Edge: RUCKUS Edge Dual-WAN enables support for two WAN links with active-backup functionality to ensure uninterrupted WAN connectivity. This feature includes monitoring mechanisms to actively check the status of the WAN links and automatically switch back to the primary active WAN link when its connection is restored.' }), status: true },
  // for testing only
  { key: TierFeatures.TEST_SELECTIVE_BETA_01, description: defineMessage({ defaultMessage: 'Test 01: Test selective 01. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum.' }), status: true },
  { key: TierFeatures.TEST_SELECTIVE_BETA_02, description: defineMessage({ defaultMessage: 'Test 02: Test selective 02. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum.' }), status: true },
  { key: TierFeatures.TEST_SELECTIVE_BETA_03, description: defineMessage({ defaultMessage: 'Test 03: Test selective 03. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum.' }), status: true }
]
