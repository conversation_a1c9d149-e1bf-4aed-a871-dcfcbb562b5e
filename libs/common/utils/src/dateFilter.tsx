import { useEffect, useMemo, useCallback, useRef } from 'react'

import { message }         from 'antd'
import { ArgsProps }       from 'antd/lib/message'
import moment              from 'moment'
import { useSearchParams } from 'react-router-dom'
import { v4 as uuidv4 }    from 'uuid'


import { DateRangeFilter, DateRange, getDateRangeFilter } from './dateUtil'
import { getIntl }                                        from './intlUtil'
import { useEncodedParameter }                            from './useEncodedParameter'

import type { Moment } from 'moment-timezone'


export interface DateFilter extends DateRangeFilter {
  initiated?: number // seconds
}

export const useDateFilter = ({
  earliestStart = undefined,
  showResetMsg = false
}: {
  earliestStart?: Moment;
  showResetMsg?: boolean;
} = {})=> {
  const { read, write } = useEncodedParameter<DateFilter>('period')
  const { $t } = getIntl()
  const period = read()
  const writeRef = useRef(write)
  writeRef.current = write

  const [, setSearch] = useSearchParams()

  const setDateFilter = useCallback((date: DateFilter) => {
    writeRef.current({
      ...date,
      initiated: (new Date()).getTime() // for when we click same relative date again
    })
  }, [])

  const result = useMemo(() => {
    const earliestStartData = (earliestStart || moment().subtract(3, 'months').subtract(1, 'hour'))
    const isSameOrAfter = period && moment(period.startDate).isSameOrAfter(earliestStartData)
    const dateFilter = isSameOrAfter
      ? getDateRangeFilter(period.range, period.startDate, period.endDate)
      : getDateRangeFilter(DateRange.last24Hours)

    return {
      dateFilter,
      setDateFilter,
      isSameOrAfter,
      ...dateFilter
    } as const
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [period, earliestStart])

  useEffect(() => {
    if (!showResetMsg) return

    const url = new URL(window.location.href)

    if (url.searchParams.get('period') && !result.isSameOrAfter) {
      const newSearch = new URLSearchParams(url.search)
      newSearch.delete('period')
      setSearch(newSearch, { replace: true })

      showToast({
        key: 'dateFilterResetToast',
        type: 'success',
        // eslint-disable-next-line max-len
        content: $t(
          // eslint-disable-next-line max-len
          { defaultMessage: 'Note that your Calendar selection has been updated in line with current page default/max values.' }
        )
      })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showResetMsg, result.isSameOrAfter])

  return result
}

type ToastType = 'info' | 'success' | 'error'

interface ToastProps extends ArgsProps {
  type: ToastType
  extraContent?: React.ReactNode
  closable?: boolean
  link?: { text?: string, onClick: Function }
}

export const showToast = (config: ToastProps): string | number => {
  const key = config.key || uuidv4()
  message.open({
    className: `toast-${config.type}`,
    key,
    // eslint-disable-next-line react/jsx-no-useless-fragment
    icon: <></>,
    duration: 7,
    ...config
  })
  return key
}

