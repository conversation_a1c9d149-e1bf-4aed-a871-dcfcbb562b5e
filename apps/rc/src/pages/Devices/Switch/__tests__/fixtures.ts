export const venuelist = {
  totalCount: 2,
  page: 1,
  data: [
    {
      country: 'United States',
      dhcp: { enabled: false, mode: 'DHCPMODE_EACH_AP' },
      enabled: false,
      mode: 'DHCPMODE_EACH_AP',
      id: '908c47ee1cd445838c3bf71d4addccdf',
      latitude: '37.4112751',
      longitude: '-122.0191908',
      name: 'Test-Venue'
    },
    {
      country: 'United States',
      dhcp: { enabled: false, mode: 'DHCPMODE_EACH_AP' },
      id: '4c778ed630394b76b17bce7fe230cf9f',
      latitude: '40.769141',
      longitude: '-73.9429713',
      name: 'My-Venue'
    },
    {
      country: 'Malaysia',
      dhcp: { enabled: true, mode: 'DHCPMODE_EACH_AP' },
      id: 'a4f9622e9c7547ba934fbb5ee55646c2',
      latitude: '4.854995099999999',
      longitude: '100.751032',
      name: 'Venue-DHCP'
    },
    {
      country: 'United States',
      dhcp: { enabled: true, mode: 'DHCPMODE_MULTIPLE_AP' },
      id: '16b11938ee934928a796534e2ee47661',
      latitude: '37.4112751',
      longitude: '-122.0191908',
      name: 'Venue-DHCP 2'
    },
    {
      country: 'Canada',
      id: 'b6cd663931b34a8b8fc97a81bfaa0929',
      latitude: '51.12090129999999',
      longitude: '-114.0044601',
      name: 'Venue-MESH'
    }
  ]
}

export const apGrouplist = {
  fields: ['name', 'id'],
  totalCount: 0,
  page: 1,
  data: [
    {
      id: '484eb4220e4b424da1f54b207cc678b9',
      name: 'test'
    }
  ]
}


export const successResponse = {
  requestId: 'request-id'
}

export const editStackData = {
  id: 'FEK4124R28X',
  venueId: '5c05180d54d84e609a4d653a3a8332d1',
  enableStack: true,
  igmpSnooping: 'none',
  jumboMode: false,
  dhcpClientEnabled: true,
  dhcpServerEnabled: false,
  rearModule: 'none'
}

export const editStackDetail = {
  suspendingDeployTime: '',
  stackMemberOrder: 'FEK4124R28X',
  isStack: true,
  rearModule: 'none',
  deviceStatus: 'ONLINE',
  syncedSwitchConfig: true,
  sendedHostname: true,
  switchMac: '',
  venueId: '5c05180d54d84e609a4d653a3a8332d1',
  model: 'ICX7150-C12P',
  id: 'FEK4124R28X',
  floorplanId: '117c43124ed24069b127c50a49a0db36',
  deviceType: 'DVCNWTYPE_SWITCH',
  serialNumber: 'FEK4124R28X',
  xPercent: 69.75138092041016,
  yPercent: 12.195121765136719,
  portsStatus: {},
  stackMember: false,
  cliApplied: false,
  stackMembers: [
    { model: 'ICX7150-C12P', id: 'FEK4124R28X' },
    { model: 'ICX7150-C12P', id: 'FEK4224R17X' }
  ],
  poeUsage: {},
  venueName: 'My-Venue',
  isIpFullContentParsed: false,
  ipFullContentParsed: true,
  formStacking: true,
  name: '',
  tenantId: 'fe892a451d7a486bbb3aee929d2dfcd1',
  activeSerial: 'FEK4124R28X'
}

export const editStackMembers = {
  data: [
    {
      venueName: 'My-Venue',
      serialNumber: 'FEK4124R28X',
      operStatusFound: false,
      switchMac: '',
      model: 'ICX7150-C12P',
      activeSerial: 'FEK4124R28X',
      id: 'FEK4124R28X',
      uptime: '',
      order: '1'
    },
    {
      venueName: 'My-Venue',
      serialNumber: 'FEK4224R17X',
      operStatusFound: false,
      switchMac: '',
      model: 'ICX7150-C12P',
      activeSerial: 'FEK4224R17X',
      id: 'FEK4224R17X',
      uptime: '',
      order: '2'
    }
  ],
  totalCount: 1
}

export const staticRoutes = [
  {
    id: '6975f36e590b43f5a47beb12af87e5f6',
    destinationIp: '0.0.0.0/1',
    nextHop: '*************',
    adminDistance: 254
  }
]

export const standaloneSwitches = [{
  id: 'FEK3224R07X', model: 'ICX7150-C12P', serialNumber: 'FEK3224R07X', name: 'FEK3224R07X_name'
}, {
  id: 'FEK3224R08X', model: 'ICX7150-C12P', serialNumber: 'FEK3224R08X', name: 'FEK3224R08X_name'
}]


export const switchVenueV1002 = [
  {
    venueId: 'a1f2bf4f969849d5a1ecfdfdb0664fac',
    venueName: 'ccc',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010_rc3'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_cd2_b4'
      },
      {
        modelGroup: 'ICX7X',
        version: '10010_rc3'
      }
    ],
    lastScheduleUpdateTime: '2024-07-09T03:59:38.071+00:00',
    preDownload: true,
    status: 'NONE',
    scheduleCount: 0
  },
  {
    venueId: 'ea1afaea242d433c86c26884adad779d',
    venueName: 'Karen-Venue-2',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010_rc2'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_rc1'
      },
      {
        modelGroup: 'ICX7X',
        version: '09010h_cd2_b4'
      }
    ],
    lastScheduleUpdateTime: '2024-06-29T10:45:43.155+00:00',
    preDownload: true,
    status: 'NONE',
    scheduleCount: 0
  },
  {
    venueId: '4d0a536ab6bf4838b43d99ae910a7edc',
    venueName: 'jk-test-venue',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010b_rc88'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_cd2_b4'
      },
      {
        modelGroup: 'ICX7X',
        version: '09010h_cd2_b4'
      }
    ],
    preDownload: false,
    status: 'NONE',
    scheduleCount: 0
  },
  {
    venueId: 'b2e9c96e150047f5a03f99dc36a34ac8',
    venueName: 'sss',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010a_cd1_b3'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_cd2_b4'
      },
      {
        modelGroup: 'ICX7X',
        version: '10010_rc2'
      }
    ],
    nextSchedule: {
      timeSlot: {
        startDateTime: '2024-05-23T02:00:00-07:00',
        endDateTime: '2024-05-23T04:00:00-07:00'
      },
      supportModelGroupVersions: [
        {
          modelGroup: 'ICX71',
          version: '09010h_rc1'
        },
        {
          modelGroup: 'ICX7X',
          version: '09010h_rc1'
        }
      ]
    },
    lastScheduleUpdateTime: '2024-07-07T10:41:37.961+00:00',
    preDownload: true,
    status: 'NONE',
    scheduleCount: 1
  },
  {
    venueId: 'd04ece51606642cca31568238ef8f977',
    venueName: 'eeew',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010_rc2'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_cd2_b4'
      },
      {
        modelGroup: 'ICX7X',
        version: '10010_rc2'
      }
    ],
    lastScheduleUpdateTime: '2024-07-02T02:38:06.961+00:00',
    preDownload: true,
    status: 'NONE',
    scheduleCount: 0
  },
  {
    venueId: 'e45b772da5504ea48af9fe472755ae22',
    venueName: 'Karen-New',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010_rc2'
      },
      {
        modelGroup: 'ICX71',
        version: '09010f_b19'
      },
      {
        modelGroup: 'ICX7X',
        version: '09010f_b19'
      }
    ],
    lastScheduleUpdateTime: '2024-07-03T01:56:49.881+00:00',
    preDownload: true,
    status: 'NONE',
    scheduleCount: 0
  },
  {
    venueId: '188c9ed87b86428b8a21246cc1f88624',
    venueName: 'My-Venue',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010a_cd3_b11'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_rc1'
      },
      {
        modelGroup: 'ICX7X',
        version: '09010h_cd2_b4'
      }
    ],
    nextSchedule: {
      timeSlot: {
        startDateTime: '2024-07-18T04:00:00-04:00',
        endDateTime: '2024-07-18T06:00:00-04:00'
      },
      supportModelGroupVersions: [
        {
          modelGroup: 'ICX82',
          version: '10010_rc2'
        },
        {
          modelGroup: 'ICX7X',
          version: '09010h_rc1'
        },
        {
          modelGroup: 'ICX71',
          version: '09010h_cd2_b4'
        }
      ]
    },
    lastScheduleUpdateTime: '2024-06-29T10:45:43.131+00:00',
    preDownload: true,
    switchCounts: [
      {
        modelGroup: 'ICX71',
        count: 1
      }
    ],
    status: 'NONE',
    scheduleCount: 1
  },
  {
    venueId: 'f3a4a81b24ad4d7b8b2ba4d6ea402b31',
    venueName: 'longlonglonglonglonglonglongvv',
    versions: [
      {
        modelGroup: 'ICX82',
        version: '10010_rc2'
      },
      {
        modelGroup: 'ICX71',
        version: '09010h_rc1'
      },
      {
        modelGroup: 'ICX7X',
        version: '09010h_rc1'
      }
    ],
    lastScheduleUpdateTime: '2024-07-07T11:06:32.307+00:00',
    preDownload: false,
    status: 'NONE',
    scheduleCount: 0
  }
]