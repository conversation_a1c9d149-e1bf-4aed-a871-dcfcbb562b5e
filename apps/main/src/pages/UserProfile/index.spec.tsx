import '@testing-library/jest-dom'
import userEvent from '@testing-library/user-event'

import { Provider }       from '@acx-ui/store'
import { render, screen } from '@acx-ui/test-utils'
import { RolesEnum }      from '@acx-ui/types'
import {
  UserProfile as UserProfileInterface,
  UserProfileContext,
  UserProfileContextProps,
  setUserProfile
}         from '@acx-ui/user'

import { UserProfile } from './index'

const params = { tenantId: 'tenant-id', activeTab: 'settings' }
const mockedUsedNavigate = jest.fn()
const userProfile = {
  initials: 'FL',
  fullName: 'First Last',
  role: RolesEnum.ADMINISTRATOR,
  email: '<EMAIL>',
  dateFormat: 'yyyy/mm/dd',
  detailLevel: 'su',
  preferredLanguage: 'en-US'
} as UserProfileInterface

jest.mock('@acx-ui/react-router-dom', () => ({
  ...jest.requireActual('@acx-ui/react-router-dom'),
  useNavigate: () => mockedUsedNavigate,
  useLocation: () => ({ state: { from: '/test' } })
}))

jest.mock('@acx-ui/msp/components', () => ({
  MultiFactor: () => <div data-testid='MultiFactor' />
}))

jest.mock('./PreferredLanguageFormItem', () => ({
  PreferredLanguageFormItem: () => <div data-testid={'rc-PreferredLanguageFormItem'}
    title='PreferredLanguageFormItem' />
}))

describe('UserProfile', () => {
  beforeAll(() => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: {
        ...window.location,
        reload: jest.fn()
      }
    })
  })
  it('should render correctly', async () => {
    setUserProfile({ profile: userProfile, allowedOperations: [] })

    render(<Provider>
      <UserProfileContext.Provider
        value={{ data: userProfile } as UserProfileContextProps}
      >
        <UserProfile />
      </UserProfileContext.Provider>
    </Provider>, { route: { params } })

    expect(screen.getByText('FL')).toBeVisible()
    expect(screen.getByText('First Last')).toBeVisible()
    expect(screen.getByText('Administrator')).toBeVisible()
    expect(screen.getByText('<EMAIL>')).toBeVisible()
    expect(screen.getByText('tenant-id')).toBeVisible()

    expect(screen.getByRole('tab', { name: 'Settings' })).toBeVisible()
    expect(screen.getByRole('tab', { name: 'Security' })).toBeVisible()
    expect(screen.getByRole('tab', { name: 'Recent Logins' })).toBeVisible()

    expect(screen.getByText('YYYY/MM/DD')).toBeVisible()
    expect(screen.getByText('Super User')).toBeVisible()
  })
  it('should navigate tabs correctly', async () => {
    setUserProfile({ profile: userProfile, allowedOperations: [] })

    render(<Provider>
      <UserProfileContext.Provider
        value={{ data: userProfile } as UserProfileContextProps}
      >
        <UserProfile />
      </UserProfileContext.Provider>
    </Provider>, { route: { params } })

    expect(screen.getByRole('tab', { name: 'Settings' })).toBeVisible()
    expect(screen.getByRole('tab', { name: 'Security' })).toBeVisible()
    expect(screen.getByRole('tab', { name: 'Recent Logins' })).toBeVisible()

    await userEvent.click(screen.getByRole('tab', { name: 'Security' }))
    expect(mockedUsedNavigate).toHaveBeenCalledWith({
      hash: '',
      pathname: '/tenant-id/t/userprofile/security',
      search: ''
    })

    await userEvent.click(screen.getByRole('tab', { name: 'Recent Logins' }))
    expect(mockedUsedNavigate).toHaveBeenCalledWith({
      hash: '',
      pathname: '/tenant-id/t/userprofile/recentLogins',
      search: ''
    })
  })
})
