import { Card }    from 'antd'
import { useIntl } from 'react-intl'

import { Features, useIsSplitOn, useIsTierAllowed }         from '@acx-ui/feature-toggle'
import { AIChat, DogAndPerson, OnboardingDog, WelcomeLogo } from '@acx-ui/icons'
import { RuckusAiDog }                                      from '@acx-ui/icons-new'
import { useUserProfileContext }                            from '@acx-ui/user'

import * as UI from './styledComponents'

function WelcomePage (props: {
  startOnboardingAssistant: ()=>void
  goChatCanvas: ()=>void
}) {
  const { $t } = useIntl()
  const { startOnboardingAssistant, goChatCanvas } = props
  const {
    data: userProfileData
  } = useUserProfileContext()
  const isInCanvasPlmList = useIsTierAllowed(Features.CANVAS)
  const isCanvasEnabled = useIsSplitOn(Features.CANVAS) || isInCanvasPlmList
  const name = userProfileData?.firstName || userProfileData?.lastName || ''
  return <div
    style={{
      position: 'relative',
      marginTop: isCanvasEnabled ? '45px':'60px',
      zIndex: 1,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      height: '100%',
      minHeight: '200px',
      maxHeight: 'calc(100vh - 250px)',
      overflowY: 'auto'
    }}
  >
    <div style={{
      height: isCanvasEnabled ? '100px':'110px'
    }} >
      {
        isCanvasEnabled ? <RuckusAiDog style={{
          width: '90px', height: '90px'
        }} />:
          <WelcomeLogo style={{
            width: '110px', height: '110px'
          }} />
      }
    </div>

    <span style={{
      fontSize: '24px',
      fontWeight: 700,
      marginTop: isCanvasEnabled ? '0px': '25px',
      fontFamily: 'Montserrat',
      ...(isCanvasEnabled && { lineHeight: '45px' })
    }}>
      {`${$t({ defaultMessage: 'Hello' })} ${name},`}
    </span>
    {
      isCanvasEnabled ? <span style={{
        fontSize: '24px',
        fontWeight: 500,
        fontFamily: 'Montserrat',
        marginBottom: '5px'
      }}>
        {$t({ defaultMessage: 'Welcome! Let' })}
        {/* <RuckusAiLogo
          style={{
            height: '20px',
            marginBottom: '-1px'
          }} /> */}
        <span
          style={{
            fontSize: '24px',
            fontWeight: 700,
            fontFamily: 'Montserrat',
            color: '#EC7100',
            margin: '0 7px 0 2px'
          }}
        > {$t({ defaultMessage: 'RUCKUS AI' })}
        </span>
        {$t({ defaultMessage: 'simplify your work' })}
      </span> : <span style={{
        fontSize: '24px',
        fontWeight: 700,
        fontFamily: 'Montserrat'
      }}>
        {$t({ defaultMessage: "I'm your personal" })}
        {/* <RuckusAiLogo
        style={{
          height: '20px',
          marginBottom: '-1px'
        }} /> */}
        <span
          style={{
            fontSize: '24px',
            fontWeight: 700,
            fontFamily: 'Montserrat',
            color: '#EC7100'
          }}
        > {$t({ defaultMessage: 'Onboarding Assistant' })}</span>
      </span>
    }
    {
      isCanvasEnabled ? <UI.WelcomeCards>
        <Card onClick={startOnboardingAssistant}>
          <UI.WelcomeMeta
            title={<span
              className='card-title'
            > <OnboardingDog />{$t({ defaultMessage: 'Onboarding Assistant' })}</span>}
            style={{ fontFamily: 'Montserrat' }}
            // eslint-disable-next-line max-len
            description={$t({ defaultMessage: 'Onboarding Assistant automates and optimizes complex network onboarding processes, leading to increased efficiency and productivity.' })}
          />
        </Card>
        <Card onClick={goChatCanvas}>
          <UI.WelcomeMeta
            title={<span
              className='card-title'
            > <AIChat />{$t({ defaultMessage: 'RUCKUS DSE' })}</span>}
            style={{ fontFamily: 'Montserrat' }}
            // eslint-disable-next-line max-len
            description={$t({ defaultMessage: 'RUCKUS Digital System Engineer (DSE) available for deployment inquiries. I can also generate on-the-fly widgets for operational data, including Alerts and Metrics.' })}
          />
        </Card>
      </UI.WelcomeCards> :
        <Card
          style={{
            width: '780px',
            margin: '100px 30px 30px 30px',
            height: '125px',
            background: '#FFFFFFCC'
          }}
        >
          <UI.WelcomeMeta
            avatar={<DogAndPerson style={{
              position: 'absolute',
              top: '-57px',
              left: '-1px',
              zIndex: '1'
            }} />}
            title={<>{$t({ defaultMessage: 'About' })} <span
              style={{
                fontSize: '18px',
                fontWeight: 700,
                fontFamily: 'Montserrat',
                color: '#EC7100'
              }}
            > {$t({ defaultMessage: 'Onboarding Assistant' })}</span></>}
            style={{ fontFamily: 'Montserrat' }}
            // eslint-disable-next-line max-len
            description={$t({ defaultMessage: 'Onboarding Assistant automates and optimizes complex network onboarding processes, leading to increased efficiency and productivity.' })}
          />
        </Card>
    }
  </div>
}

export default WelcomePage
