import React, { createContext, Dispatch, SetStateAction, useContext, useEffect, useState, useRef } from 'react'

import { Divider, Menu, Space } from 'antd'
import moment                   from 'moment-timezone'
import { DndProvider }          from 'react-dnd'
import { HTML5Backend }         from 'react-dnd-html5-backend'
import { useIntl }              from 'react-intl'

import {
  ClientExperience,
  ConnectedClientsOverTime,
  DidYouKnow,
  IncidentsDashboardv2,
  SwitchesTrafficByVolume,
  TopAppsByTraffic,
  TopEdgesByResources,
  TopEdgesByTraffic,
  TopSwitchesByError,
  TopSwitchesByPoEUsage,
  TopSwitchesByTraffic,
  TopSwitchModels,
  TopWiFiNetworks,
  TrafficByVolume
} from '@acx-ui/analytics/components'
import {
  Button,
  ContentSwitcher,
  ContentSwitcherProps,
  Dropdown,
  GridCol,
  GridRow,
  Loader,
  PageHeader,
  RangePicker,
  Select,
  useLayoutContext
} from '@acx-ui/components'
import { Features, useIsSplitOn, useIsTierAllowed } from '@acx-ui/feature-toggle'
import {
  GlobeOutlined,
  LockOutlined,
  SettingsOutlined
} from '@acx-ui/icons-new'
import { VenueFilter }      from '@acx-ui/main/components'
import {
  AlarmWidgetV2,
  ClientsWidgetV2,
  DevicesDashboardWidgetV2,
  MapWidgetV2,
  useIsEdgeReady,
  VenuesDashboardWidgetV2
} from '@acx-ui/rc/components'
import {
  useGetDashboardsQuery,
  usePatchDashboardMutation,
  useUpdateDashboardsMutation
} from '@acx-ui/rc/services'
import {
  Canvas,
  CanvasInfo,
  CommonUrlsInfo,
  DashboardInfo,
  EdgeUrlsInfo,
  SwitchRbacUrlsInfo,
  WifiRbacUrlsInfo
} from '@acx-ui/rc/utils'
import { TenantLink }     from '@acx-ui/react-router-dom'
import { UseQueryResult } from '@acx-ui/types'
import {
  EdgeScopes,
  RolesEnum,
  SwitchScopes,
  WifiScopes
}                                               from '@acx-ui/types'
import {
  hasCrossVenuesPermission,
  filterByAccess,
  getShowWithoutRbacCheckKey,
  hasPermission,
  hasRoles,
  getUserProfile,
  hasAllowedOperations,
  isCoreTier,
  useUserProfileContext
} from '@acx-ui/user'
import {
  AnalyticsFilter,
  DateFilter,
  DateRange,
  getDatePickerValues,
  LoadTimeContext,
  getDateRangeFilter,
  getOpsApi,
  useDashboardFilter
} from '@acx-ui/utils'

import AICanvasModal                                                     from '../AICanvas'
import { CardInfo, layoutConfig, Section, Group }                        from '../AICanvas/Canvas'
import Layout                                                            from '../AICanvas/components/Layout'
import { DEFAULT_DASHBOARD_ID, getCalculatedColumnWidth, getCanvasData } from '../AICanvas/index.utils'
import { PreviewDashboardModal }                                         from '../AICanvas/PreviewDashboardModal'
import * as CanvasUI                                                     from '../AICanvas/styledComponents'

import { DashboardDrawer }       from './DashboardDrawer'
import { ImportDashboardDrawer } from './ImportDashboardDrawer'
import { formatDashboardList }   from './index.utils'
import * as UI                   from './styledComponents'

interface DashboardFilterContextProps {
  dashboardFilters: AnalyticsFilter;
  setDateFilterState: Dispatch<SetStateAction<DateFilter>>;
}

const DashboardFilterContext = createContext<DashboardFilterContextProps>({
  dashboardFilters: getDateRangeFilter(DateRange.last8Hours) as AnalyticsFilter,
  setDateFilterState: () => {}
})

export const DashboardFilterProvider = ({ children }: { children : React.ReactNode }) => {
  const [dateFilterState, setDateFilterState] = useState<DateFilter>(
    getDateRangeFilter(DateRange.last8Hours)
  )
  const { filters } = useDashboardFilter()
  const { startDate, endDate, range } = getDatePickerValues(dateFilterState)
  const dashboardFilters = { ...filters, startDate, endDate, range }

  return (
    <DashboardFilterContext.Provider value={{ dashboardFilters, setDateFilterState }}>
      {children}
    </DashboardFilterContext.Provider>
  )
}

export const useDashBoardUpdatedFilter = () => {
  const context = useContext(DashboardFilterContext)
  return context
}
export default function Dashboard () {
  const isInCanvasPlmList = useIsTierAllowed(Features.CANVAS)
  const isCanvasEnabled = useIsSplitOn(Features.CANVAS) || isInCanvasPlmList
  const [canvasId, setCanvasId] = useState('')
  const [groups, setGroups] = useState([] as Group[])
  const [sections, setSections] = useState([] as Section[])
  const [dashboardId, setDashboardId] = useState('')
  const [initDashboardId, setInitDashboardId] = useState(false)
  const [dashboardList, setDashboardList] = useState([] as DashboardInfo[])

  const { isCustomPrivilegeGroup } = useUserProfileContext()
  const isAdminUser = hasRoles([RolesEnum.PRIME_ADMIN, RolesEnum.ADMINISTRATOR])
  const isDashboardCanvasEnabled = isCanvasEnabled && isAdminUser && !isCustomPrivilegeGroup
  const getDashboardsQuery = useGetDashboardsQuery({}, { skip: !isDashboardCanvasEnabled })
  const { data: dashboards, isLoading: dashboardsLoading } = getDashboardsQuery

  useEffect(() => {
    if (!isDashboardCanvasEnabled) {
      setDashboardId(DEFAULT_DASHBOARD_ID)
    }
  }, [])

  useEffect(() => {
    if (isCanvasEnabled && dashboards?.length) {
      const updatedDashboards = formatDashboardList(dashboards)
      const dashboardIds = updatedDashboards.map(item => item.id)
      if (!initDashboardId) {
        setInitDashboardId(true)
        setDashboardId(dashboardIds[0])
      } else if (!dashboardIds.includes(dashboardId)) {
        setDashboardId(dashboardIds[0])
      }
      setDashboardList(updatedDashboards)
    }
  }, [dashboards])

  useEffect(() => {
    if (isDashboardCanvasEnabled && !!dashboardId && dashboardList.length) {
      if (dashboardId !== DEFAULT_DASHBOARD_ID) {
        const selectedDashboard = dashboardList.filter(item => item.id === dashboardId)
        if (selectedDashboard) {
          const { canvasId, sections, groups } = getCanvasData(
            selectedDashboard as unknown as Canvas[]
          )
          if (canvasId && sections) {
            setCanvasId(canvasId)
            setSections(sections)
            setGroups(groups)
          }
        }
      } else if (canvasId) {
        setCanvasId('')
        setSections([])
        setGroups([])
      }
    }
  }, [dashboardId, dashboardList])

  return (
    <DashboardFilterProvider>
      <DashboardPageHeader
        dashboardId={dashboardId}
        setDashboardId={setDashboardId}
        dashboardList={dashboardList}
        getDashboardsQuery={getDashboardsQuery}
      />
      {
        <Loader states={[{ isLoading: isDashboardCanvasEnabled ? dashboardsLoading : false }]}>{
          dashboardId === DEFAULT_DASHBOARD_ID
            ? <DefaultDashboard />
            : <CanvasDashboard
              canvasId={canvasId}
              sections={sections}
              groups={groups}
              setGroups={setGroups} />
        }</Loader>
      }
    </DashboardFilterProvider>
  )
}

function DashboardPageHeader (props: {
  dashboardId: string,
  setDashboardId: (id: string) => void
  dashboardList: DashboardInfo[]
  getDashboardsQuery: UseQueryResult<DashboardInfo[]>
}) {
  const { dashboardId, setDashboardId, dashboardList } = props
  const { dashboardFilters, setDateFilterState } = useDashBoardUpdatedFilter()
  const { onPageFilterChange } = useContext(LoadTimeContext)

  const { startDate , endDate, range } = dashboardFilters
  const { rbacOpsApiEnabled } = getUserProfile()
  const { $t } = useIntl()
  const isEdgeEnabled = useIsEdgeReady()
  const isInCanvasPlmList = useIsTierAllowed(Features.CANVAS)
  const isCanvasEnabled = useIsSplitOn(Features.CANVAS) || isInCanvasPlmList
  const isDateRangeLimit = useIsSplitOn(Features.ACX_UI_DATE_RANGE_LIMIT)

  const { isCustomPrivilegeGroup } = useUserProfileContext()
  const isAdminUser = hasRoles([RolesEnum.PRIME_ADMIN, RolesEnum.ADMINISTRATOR])
  const isDashboardCanvasEnabled = isCanvasEnabled && isAdminUser && !isCustomPrivilegeGroup

  const [canvasModalVisible, setCanvasModalVisible] = useState(false)
  const [editCanvasId, setEditCanvasId] = useState<undefined | string>(undefined)
  const [previewData, setPreviewData] = useState([] as Canvas[])
  const [previewModalVisible, setPreviewModalVisible] = useState(false)
  const [dashboardDrawerVisible, setDashboardDrawerVisible] = useState(false)
  const [importDashboardDrawerVisible, setImportDashboardDrawerVisible] = useState(false)
  const [updateDashboards] = useUpdateDashboardsMutation()
  const [patchDashboard] = usePatchDashboardMutation()
  const isInitDashboardCheckedRef = useRef<boolean | undefined>(false)
  const shouldCleanupDashboardIdRef = useRef<string | undefined>(undefined)

  const hasCreatePermission = hasPermission({
    scopes: [WifiScopes.CREATE, SwitchScopes.CREATE, EdgeScopes.CREATE],
    rbacOpsIds: [
      getOpsApi(WifiRbacUrlsInfo.addAp),
      getOpsApi(SwitchRbacUrlsInfo.addSwitch),
      [
        getOpsApi(EdgeUrlsInfo.addEdge),
        getOpsApi(EdgeUrlsInfo.addEdgeCluster)
      ]
    ]
  })

  const hasAddVenuePermission = rbacOpsApiEnabled ?
    hasAllowedOperations([getOpsApi(CommonUrlsInfo.addVenue)])
    : hasRoles([RolesEnum.PRIME_ADMIN, RolesEnum.ADMINISTRATOR]) &&
  hasCrossVenuesPermission()

  const hasAddNetworkPermission = rbacOpsApiEnabled ?
    hasAllowedOperations([getOpsApi(WifiRbacUrlsInfo.addNetworkDeep)])
    : hasPermission({ scopes: [WifiScopes.CREATE] }) &&
  hasCrossVenuesPermission()

  const addMenu = <Menu
    expandIcon={<UI.MenuExpandArrow />}
    items={[
      ...(hasAddVenuePermission ? [{
        key: 'add-venue',
        label: <TenantLink to='venues/add'>
          {$t({ defaultMessage: '<VenueSingular></VenueSingular>' })}
        </TenantLink>
      }]: []),
      ...(hasAddNetworkPermission ? [{
        key: 'add-wifi-network',
        label: <TenantLink to='networks/wireless/add'>{
          $t({ defaultMessage: 'Wi-Fi Network' })}
        </TenantLink>
      }] : []),
      ...( hasCreatePermission ? [{
        key: 'add-device',
        label: $t({ defaultMessage: 'Device' }),
        // type: 'group',
        children: [
          ...( hasPermission({ scopes: [WifiScopes.CREATE],
            rbacOpsIds: [getOpsApi(WifiRbacUrlsInfo.addAp)] }) ? [{
              key: 'add-ap',
              label: <TenantLink to='devices/wifi/add'>
                {$t({ defaultMessage: 'Wi-Fi AP' })}
              </TenantLink>
            }] : []),
          ...( hasPermission({ scopes: [SwitchScopes.CREATE],
            rbacOpsIds: [getOpsApi(SwitchRbacUrlsInfo.addSwitch)]
          }) ? [{
              key: 'add-switch',
              label: <TenantLink to='devices/switch/add'>
                {$t({ defaultMessage: 'Switch' })}
              </TenantLink>
            }] : []),
          ...(isEdgeEnabled &&
            hasPermission({
              scopes: [EdgeScopes.CREATE],
              rbacOpsIds: [
                [
                  getOpsApi(EdgeUrlsInfo.addEdge),
                  getOpsApi(EdgeUrlsInfo.addEdgeCluster)
                ]
              ]
            })) ? [{
              key: 'add-edge',
              label: <TenantLink to='devices/edge/add'>{
                $t({ defaultMessage: 'RUCKUS Edge' })
              }</TenantLink>
            }] : []
        ]
      }] : [])
    ]}
  />

  useEffect(() => {
    onPageFilterChange?.(dashboardFilters, true)
    return () => {
      if (isCanvasEnabled && shouldCleanupDashboardIdRef.current) {
        handleClearNotifications(shouldCleanupDashboardIdRef.current)
      }
    }
  }, [])

  useEffect(() => {
    if (dashboardId && dashboardList.length && !isInitDashboardCheckedRef.current) {
      const currentDashboard = dashboardList.find(item => item.id === dashboardId)
      if (currentDashboard && hasDashboardChanged(currentDashboard)) {
        shouldCleanupDashboardIdRef.current = dashboardId
      }
      isInitDashboardCheckedRef.current = true
    }
  }, [dashboardId])

  useEffect(() => {
    onPageFilterChange?.(dashboardFilters)
  }, [dashboardFilters])

  const hasDashboardChanged = (dashboard?: DashboardInfo) =>
    !!dashboard?.authorId && !!dashboard?.diffWidgetIds?.length

  const handleClearNotifications = async (value: string) => {
    await patchDashboard({
      params: { dashboardId: value }
    })
    shouldCleanupDashboardIdRef.current = undefined
  }

  const handleChangeDashboard = async (value: string) => {
    const currentDashboard = dashboardList.find(item => item.id === dashboardId)
    const newDashboard = dashboardList.find(item => item.id === value)
    if (currentDashboard && hasDashboardChanged(currentDashboard)) {
      handleClearNotifications(currentDashboard.id)
    }
    if (newDashboard && hasDashboardChanged(newDashboard)) {
      shouldCleanupDashboardIdRef.current = value
    }
    setDashboardId(value)
  }

  const handleOpenPreview = async (data: Canvas[] | DashboardInfo[] | CanvasInfo[]) => {
    if (data) {
      setPreviewData(data as unknown as Canvas[])
      setPreviewModalVisible(true)
    }
  }

  const handleOpenCanvas = async (id?: string) => {
    setEditCanvasId(id ?? undefined)
    setCanvasModalVisible(true)
  }

  const DashboardSelector = () => {
    return <>
      <UI.DashboardSelectDropdown />
      <UI.DashboardSelector
        defaultActiveFirstOption
        defaultValue={dashboardId}
        dropdownMatchSelectWidth={false}
        dropdownClassName='dashboard-select-dropdown'
        optionLabelProp='label'
        onChange={handleChangeDashboard}
      >{
          dashboardList.map(item => {
            const isDefault = item.id === DEFAULT_DASHBOARD_ID
            const hasUpdated = !!item.authorId && !!item.diffWidgetIds?.length
            const icon = item.visible || isDefault
              ? <GlobeOutlined size='sm' /> : <LockOutlined size='sm' />

            return <Select.Option
              key={item.id}
              value={item.id}
              label={item.name}
              className={isDefault ? 'default' : (hasUpdated ? 'hasUpdated' : '')}
            >
              { icon }{ item.name }
            </Select.Option>
          })
        }</UI.DashboardSelector>
    </>
  }

  return (<>
    <PageHeader
      title={''}
      titleExtra={isCanvasEnabled && dashboardList?.length &&
      <Space size={7} style={{ alignItems: 'center', lineHeight: 1 }}>
        <DashboardSelector />
        <Button
          data-testid='setting-button'
          ghost={true}
          icon={<SettingsOutlined size='sm' />}
          style={{ minWidth: '16px', width: '16px' }}
          onClick={()=> {
            setDashboardDrawerVisible(true)
          }}
        />
      </Space>}
      extra={[
        ...filterByAccess([
          <Dropdown overlay={addMenu}
            placement={'bottomRight'}
            rbacOpsIds={[
              getOpsApi(WifiRbacUrlsInfo.addAp),
              getOpsApi(SwitchRbacUrlsInfo.addSwitch),
              [
                getOpsApi(EdgeUrlsInfo.addEdge),
                getOpsApi(EdgeUrlsInfo.addEdgeCluster)
              ],
              getOpsApi(WifiRbacUrlsInfo.addNetworkDeep),
              getOpsApi(CommonUrlsInfo.addVenue)
            ]}
            scopeKey={[WifiScopes.CREATE, SwitchScopes.CREATE, EdgeScopes.CREATE]}>{() =>
              <Button type='primary'>{ $t({ defaultMessage: 'Add...' }) }</Button>
            }</Dropdown>
        ]),
        <VenueFilter
          disabled={dashboardId !== DEFAULT_DASHBOARD_ID}
          key={getShowWithoutRbacCheckKey('hierarchy-filter')}
        />,
        dashboardId === DEFAULT_DASHBOARD_ID && <RangePicker
          key={getShowWithoutRbacCheckKey('range-picker')}
          selectedRange={{ startDate: moment(startDate), endDate: moment(endDate) }}
          onDateApply={setDateFilterState as CallableFunction}
          showTimePicker
          selectionType={range}
          showLast8hours
          maxMonthRange={isDateRangeLimit ? 1 : 3}
        />
      ]}
      style={{ marginBottom: '12px' }}
    />

    { isDashboardCanvasEnabled && <>
      <DashboardDrawer
        data={dashboardList}
        visible={dashboardDrawerVisible}
        handleOpenPreview={handleOpenPreview}
        handleOpenCanvas={handleOpenCanvas}
        onClose={() => {
          setDashboardDrawerVisible(false)
        }}
        onNextClick={() => {
          setImportDashboardDrawerVisible(true)
        }}
      />

      <ImportDashboardDrawer
        visible={importDashboardDrawerVisible}
        dashboardList={dashboardList}
        handleOpenPreview={handleOpenPreview}
        handleOpenCanvas={handleOpenCanvas}
        onBackClick={() => {
          setDashboardDrawerVisible(true)
          setImportDashboardDrawerVisible(false)
        }}
        onImportClick={async (keys) => {
          await updateDashboards({
            payload: keys
          }).then(() => {
            props.getDashboardsQuery.refetch()
            setImportDashboardDrawerVisible(false)
          })
        }}
        onClose={() => setImportDashboardDrawerVisible(false)}
      />

      <PreviewDashboardModal
        data={previewData}
        visible={previewModalVisible}
        setVisible={setPreviewModalVisible}
        DefaultDashboard={DefaultDashboard}
      />

      <AICanvasModal
        isModalOpen={canvasModalVisible}
        setIsModalOpen={setCanvasModalVisible}
        editCanvasId={editCanvasId}
        openNewCanvas={editCanvasId ? !editCanvasId : true}
      />

    </>}

  </>
  )
}

function ApWidgets () {
  const { dashboardFilters } = useDashBoardUpdatedFilter()
  const { accountTier } = getUserProfile()
  const isCore = isCoreTier(accountTier)

  return (
    <GridRow>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TrafficByVolume filters={dashboardFilters} vizType={'area'} />
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <ConnectedClientsOverTime filters={dashboardFilters} vizType={'area'} />
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopWiFiNetworks filters={dashboardFilters}/>
      </GridCol>
      {!isCore && <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopAppsByTraffic filters={dashboardFilters}/>
      </GridCol>}

    </GridRow>
  )
}

function DashboardMapWidget () {
  return (
    <GridRow>
      <GridCol col={{ span: 24 }} style={{ height: '428px' }}>
        <MapWidgetV2 />
      </GridCol>
    </GridRow>
  )
}

function SwitchWidgets () {
  const { dashboardFilters } = useDashBoardUpdatedFilter()
  return (
    <GridRow>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <SwitchesTrafficByVolume filters={dashboardFilters} vizType={'area'} />
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopSwitchesByPoEUsage filters={dashboardFilters}/>
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopSwitchesByTraffic filters={dashboardFilters}/>
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopSwitchesByError filters={dashboardFilters} />
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopSwitchModels filters={dashboardFilters}/>
      </GridCol>
    </GridRow>
  )
}

function EdgeWidgets () {
  const { dashboardFilters } = useDashBoardUpdatedFilter()
  return (
    <GridRow>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopEdgesByTraffic filters={dashboardFilters} />
      </GridCol>
      <GridCol col={{ span: 12 }} style={{ height: '280px' }}>
        <TopEdgesByResources filters={dashboardFilters} />
      </GridCol>
    </GridRow>
  )
}

function CoreDashboardWidgets () {
  const { dashboardFilters } = useDashBoardUpdatedFilter()

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  return (
    <GridRow>
      <GridCol col={{ span: 18 }} style={{ height: '410px' }}>
        <GridRow>
          <GridCol col={{ span: 12 }} style={{ height: '200px' }}>
            <AlarmWidgetV2 />
          </GridCol>
          <GridCol col={{ span: 12 }} style={{ height: '200px' }}>
            <VenuesDashboardWidgetV2 />
          </GridCol>
        </GridRow>
        <GridRow style={{ marginTop: '10px' }}>
          <GridCol col={{ span: 12 }} style={{ height: '200px' }}>
            <DevicesDashboardWidgetV2 />
          </GridCol>
          <GridCol col={{ span: 12 }} style={{ height: '200px' }}>
            <ClientsWidgetV2 />
          </GridCol>
        </GridRow>
      </GridCol>
      <GridCol col={{ span: 6 }} style={{ height: '410px' }}>
        <DidYouKnow filters={dashboardFilters}/>
      </GridCol>
    </GridRow>
  )
}


function CommonDashboardWidgets () {
  const { dashboardFilters } = useDashBoardUpdatedFilter()

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  return (
    <GridRow>
      <GridCol col={{ span: 18 }} style={{ height: '410px' }}>
        <GridRow>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <AlarmWidgetV2 />
          </GridCol>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <IncidentsDashboardv2 filters={dashboardFilters} />
          </GridCol>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <ClientExperience filters={dashboardFilters}/>
          </GridCol>
        </GridRow>
        <GridRow style={{ marginTop: '10px' }}>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <VenuesDashboardWidgetV2 />
          </GridCol>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <DevicesDashboardWidgetV2 />
          </GridCol>
          <GridCol col={{ span: 8 }} style={{ height: '200px' }}>
            <ClientsWidgetV2 />
          </GridCol>
        </GridRow>
      </GridCol>
      <GridCol col={{ span: 6 }} style={{ height: '410px' }}>
        <DidYouKnow filters={dashboardFilters}/>
      </GridCol>
    </GridRow>
  )
}

function DeviceWidgetsAndMapWidget (props: {
  tabDetails: ContentSwitcherProps['tabDetails'],
  onTabChange: (value: string) => void,
  enabledUXOptFeature: boolean
}) {
  const { $t } = useIntl()
  const { tabDetails, onTabChange, enabledUXOptFeature } = props

  return <>
    <Divider dashed
      style={{ borderColor: 'var(--acx-neutrals-30)', margin: '20px 0px 5px 0px' }}
    />
    <ContentSwitcher
      tabId={'dashboard-devices'}
      tabDetails={tabDetails}
      size='large'
      defaultValue={localStorage.getItem('dashboard-tab') || tabDetails[0].value}
      onChange={onTabChange}
      extra={
        <UI.Wrapper>
          <TenantLink to={'/reports'}>
            {$t({ defaultMessage: 'See more reports' })} <UI.ArrowChevronRightIcons />
          </TenantLink>
        </UI.Wrapper>
      }
      tabPersistence={enabledUXOptFeature}
    />
    <Divider dashed
      style={{ borderColor: 'var(--acx-neutrals-30)', margin: '20px 0px' }}
    />
    <DashboardMapWidget />
  </>
}

function DefaultDashboard () {
  const { $t } = useIntl()
  const { accountTier } = getUserProfile()
  const isCore = isCoreTier(accountTier)
  const isEdgeEnabled = useIsEdgeReady()
  const enabledUXOptFeature = useIsSplitOn(Features.UX_OPTIMIZATION_FEATURE_TOGGLE)

  const tabDetails: ContentSwitcherProps['tabDetails'] = [
    {
      label: $t({ defaultMessage: 'Wi-Fi' }),
      value: 'ap',
      children: <ApWidgets />
    },
    {
      label: $t({ defaultMessage: 'Switch' }),
      value: 'switch',
      children: <SwitchWidgets />
    },
    ...(isEdgeEnabled ? [
      {
        label: $t({ defaultMessage: 'RUCKUS Edge' }),
        value: 'edge',
        children: <EdgeWidgets />
      }
    ] : [])
  ]

  /**
   * Sets the selected tab value in local storage.
   *
   * @param {string} value - The value of the selected tab.
   * @return {void} This function does not return anything.
   */
  const onTabChange = (value: string): void => {
    localStorage.setItem('dashboard-tab', value)
  }

  return <>
    {isCore ? <CoreDashboardWidgets /> : <CommonDashboardWidgets />}
    <DeviceWidgetsAndMapWidget
      tabDetails={tabDetails}
      onTabChange={onTabChange}
      enabledUXOptFeature={enabledUXOptFeature}
    />
  </>
}

function CanvasDashboard (props: {
  canvasId: string
  sections: Section[]
  groups: Group[]
  setGroups: React.Dispatch<React.SetStateAction<Group[]>>
}) {
  const { canvasId, sections, groups, setGroups } = props
  const { isCustomPrivilegeGroup } = useUserProfileContext()
  const isAdminUser = hasRoles([RolesEnum.PRIME_ADMIN, RolesEnum.ADMINISTRATOR])
  const isInCanvasPlmList = useIsTierAllowed(Features.CANVAS)
  const isCanvasEnabled = useIsSplitOn(Features.CANVAS) || isInCanvasPlmList
  const isDashboardCanvasEnabled = isCanvasEnabled && isAdminUser && !isCustomPrivilegeGroup

  const { menuCollapsed } = useLayoutContext()
  const [layout, setLayout] = useState({
    ...layoutConfig,
    calWidth: getCalculatedColumnWidth(menuCollapsed)
  })
  const [shadowCard, setShadowCard] = useState({} as CardInfo)

  useEffect(() => {
    if (isDashboardCanvasEnabled) {
      setLayout({
        ...layout,
        calWidth: getCalculatedColumnWidth(menuCollapsed)
      })
    }
  }, [menuCollapsed])

  return <DndProvider backend={HTML5Backend}>
    <div className='grid' style={{ marginTop: '-10px' }}>
      <CanvasUI.Grid $type='pageview'>
        <Layout
          readOnly={true}
          sections={sections}
          groups={groups}
          setGroups={setGroups}
          compactType={'horizontal'}
          layout={layout}
          setLayout={setLayout}
          canvasId={canvasId}
          shadowCard={shadowCard}
          setShadowCard={setShadowCard}
          containerId='dashboard-canvas-container'
        />
      </CanvasUI.Grid>
    </div>
  </DndProvider>
}