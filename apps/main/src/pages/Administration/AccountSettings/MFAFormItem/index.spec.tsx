/* eslint-disable max-len */
import _        from 'lodash'
import { rest } from 'msw'

import { AdministrationUrlsInfo } from '@acx-ui/rc/utils'
import { Provider  }              from '@acx-ui/store'
import {
  mockServer,
  render,
  screen,
  fireEvent,
  waitFor
} from '@acx-ui/test-utils'
import { UserUrlsInfo, MFAStatus, setUserProfile, getUserProfile } from '@acx-ui/user'

import { fakeMFATenantDetail, fakeTenantDetails } from '../__tests__/fixtures'

import  { MFAFormItem } from './'

const mockedNavigatorWriteText = jest.fn()
const mockedUsedNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedUsedNavigate
}))
describe('Enable MFA Checkbox', () => {
  const params: { tenantId: string } = { tenantId: 'ecc2d7cf9d2342fdb31ae0e24958fcac' }

  beforeEach(() => {
    mockServer.use(
      rest.put(
        UserUrlsInfo.toggleMFA.url,
        (_req, res, ctx) => res(ctx.status(200))
      ),
      rest.get(
        AdministrationUrlsInfo.getTenantDetails.url,
        (_req, res, ctx) => res(ctx.json(fakeTenantDetails))
      )
    )

    Object.assign(window.navigator, {
      clipboard: {
        writeText: mockedNavigatorWriteText
      }
    })
  })

  it('should display confirm dialog after enable MFA checkbox changed', async () => {
    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={fakeMFATenantDetail}
          isPrimeAdminUser={true}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).not.toBeChecked()
    fireEvent.click(formItem)

    const okBtn = await screen.findByRole('button', { name: 'Enable MFA' })
    expect(okBtn).toBeVisible()

    fireEvent.click(okBtn)
    await waitFor(() => {
      expect(okBtn).not.toBeVisible()
    })
    expect(mockedUsedNavigate).toHaveBeenCalledWith({
      pathname: `/${params.tenantId}/t/userprofile/security`,
      hash: '',
      search: ''
    })
  })

  it('should successfully copy codes', async () => {
    const enabledData = _.cloneDeep(fakeMFATenantDetail)
    enabledData.tenantStatus = MFAStatus.ENABLED

    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={enabledData}
          isPrimeAdminUser={true}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).toBeChecked()

    const copyBtn = await screen.findByText( 'Copy Codes' )
    fireEvent.click(copyBtn)
    expect(mockedNavigatorWriteText).toBeCalledWith('678490\n287605\n230202\n791760\n169187')
  })

  it('should correctly display when click to disable MFA', async () => {
    const enabledData = _.cloneDeep(fakeMFATenantDetail)
    enabledData.tenantStatus = MFAStatus.ENABLED

    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={enabledData}
          isPrimeAdminUser={true}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).toBeChecked()
    fireEvent.click(formItem)

    const okBtn = await screen.findByRole('button', { name: 'Disable MFA' })
    expect(okBtn).toBeVisible()
    fireEvent.click(okBtn)
    await waitFor(() => {
      expect(okBtn).not.toBeVisible()
    })
  })


  it('should display error when click to disable MFA', async () => {
    const spyLog = jest.spyOn(console, 'log')

    mockServer.use(
      rest.put(
        UserUrlsInfo.toggleMFA.url,
        (_req, res, ctx) => res(ctx.status(500), ctx.json(null))
      )
    )

    const enabledData = _.cloneDeep(fakeMFATenantDetail)
    enabledData.tenantStatus = MFAStatus.ENABLED

    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={enabledData}
          isPrimeAdminUser={true}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).toBeChecked()
    fireEvent.click(formItem)

    const okBtn = await screen.findByRole('button', { name: 'Disable MFA' })
    expect(okBtn).toBeVisible()
    fireEvent.click(okBtn)
    // FIXME: might need to fix when general error handler behavior changed.
    await waitFor(() => {
      expect(spyLog).toBeCalledWith({ data: null, status: 500 })
    })
    await waitFor(() => {
      expect(okBtn).not.toBeVisible()
    })
  })

  it('should be disabled to click toggle MFA', async () => {
    setUserProfile({
      ...getUserProfile(),
      rbacOpsApiEnabled: true
    })
    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={fakeMFATenantDetail}
          isPrimeAdminUser={false}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).toBeDisabled()
  })

  it('should display correctly if no data', async () => {
    setUserProfile({
      ...getUserProfile(),
      rbacOpsApiEnabled: true
    })
    render(
      <Provider>
        <MFAFormItem
          mfaTenantDetailsData={undefined}
          isPrimeAdminUser={false}
          isMspEc={false}
        />
      </Provider>, {
        route: { path: '/:tenantId/t', params }
      })

    const formItem = screen.getByRole('checkbox', { name: /Enable Multi-Factor Authentication/i })
    expect(formItem).toBeDisabled()
    const copyBtn = await screen.findByText( 'Copy Codes' )
    fireEvent.click(copyBtn)
    expect(mockedNavigatorWriteText).toBeCalledWith('')
  })
})
