{"sourceRoot": "apps/main/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/web:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/main", "index": "apps/main/src/index.html", "baseHref": "/tenant/t/", "main": "apps/main/src/main.ts", "polyfills": "apps/main/src/polyfills.ts", "tsConfig": "apps/main/tsconfig.app.json", "assets": ["apps/main/src/favicon.ico", "apps/main/src/globalValues.json", "apps/main/src/assets", "apps/main/src/locales", "apps/main/src/robots.txt", {"glob": "**/*", "input": "libs/main/assets/", "output": "./assets/"}, {"glob": "**/*", "input": "libs/assets/", "output": "./assets/"}], "styles": [], "scripts": [], "webpackConfig": "apps/main/webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "webpackConfig": "apps/main/webpack.config.prod.js"}}}, "serve": {"executor": "@nrwl/react:module-federation-dev-server", "defaultConfiguration": "development", "options": {"baseHref": "/", "buildTarget": "main:build", "hmr": true, "port": 3000}, "configurations": {"development": {"buildTarget": "main:build:development"}, "production": {"buildTarget": "main:build:production", "hmr": false}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/main/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/main"], "options": {"jestConfig": "apps/main/jest.config.ts", "passWithNoTests": true}}, "serve-static": {"executor": "@nrwl/web:file-server", "defaultConfiguration": "development", "options": {"buildTarget": "main:build", "port": 3000}, "configurations": {"development": {"buildTarget": "main:build:development"}, "production": {"buildTarget": "main:build:production"}}}}, "tags": ["acx-app"]}