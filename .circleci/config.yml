version: 2.1

jobs:
  build:
    docker:
      - image: cimg/base:stable
    steps:
      - setup_remote_docker:
          version: default
      - checkout
      - run:
          name: Build
          command: docker build --build-arg CI_COMMIT_SHA=$CIRCLE_SHA1 --tag ${GCR_HOSTNAME}/${GCR_PROJECT_ID}/acx-ui:${CIRCLE_SHA1:0:7} -f ./Dockerfile.ra .
      - run:
          name: Push
          command: |
            docker logout > /dev/null 2>&1
            echo "$GCR_KEY" | docker login -u _json_key --password-stdin https://${GCR_HOSTNAME}
            docker push ${GCR_HOSTNAME}/${GCR_PROJECT_ID}/acx-ui:${CIRCLE_SHA1:0:7}

workflows:
  version: 2
  run_ci:
    jobs:
      - build:
          context: org-global
          filters:
            branches:
              only: master
